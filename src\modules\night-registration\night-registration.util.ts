import dayjs from 'dayjs';

import { DATE_FORMAT_ABBREVIATION } from '~/constants/app.constant';
import {
  NightRegistrationGender,
  NightRegistrationWarningLevel,
  NRMaximumStayDurationLevel,
  NRMaximumStayDurationLevelLabel,
} from '~/shared/enums/night-registration.enum';

export const HEADERS = {
  FIRST_NAME: 'First name',
  LAST_NAME: 'Last name',
  DOB: 'Date of birth',
  CLIENT_ID: 'Client ID',
  CUSTOMER: 'Customer',
  PHONE_NUM: 'Phone number',
  EMAIL: 'Email',
  NATIONALITY: 'Nationality',
  GENDER: 'Gender',
};

export function validateEmail(index, email, errorsMissing, errorsInvalid) {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!email) {
    errorsMissing.push({
      line: index + 2,
      field: HEADERS.EMAIL,
      message: 'Missing email',
    });
  }
  if (email && !emailRegex.test(email)) {
    errorsInvalid.push({
      line: index + 2,
      field: HEADERS.EMAIL,
      message: 'Invalid email',
    });
  }
}

export function validatePhoneNumber(index, phoneNumber, errorsInvalid) {
  const phoneNumberRegex = /^(\+|)(?!0+$)[0-9]{1,24}$/;
  if (phoneNumber && !phoneNumberRegex.test(phoneNumber)) {
    errorsInvalid.push({
      line: index + 2,
      field: HEADERS.PHONE_NUM,
      message: 'Invalid phone number that must be 10-24 digits',
    });
  }
}

export function validateClientId(index, clientId, errorsInvalid) {
  const clientIdRegex = /^(?!0+$)[a-zA-Z0-9]{1,15}$/;
  if (clientId && !clientIdRegex.test(clientId)) {
    errorsInvalid.push({
      line: index + 2,
      field: HEADERS.CLIENT_ID,
      message: 'Invalid client ID that must be 1-15 characters',
    });
  }
}

export function validateGender(index, gender, errorsInvalid) {
  if (!gender) {
    return;
  } else if (!['MALE', 'M', 'FEMALE', 'F'].includes(gender)) {
    errorsInvalid.push({
      line: index + 2,
      field: HEADERS.GENDER,
      message: 'Invalid gender that must be [MALE, M, FEMALE, F]',
    });
  }
}

export function validateDateOfBirthFormat(
  index,
  date,
  errorsMissing,
  errorsInvalid,
) {
  if (!date) {
    errorsMissing.push({
      line: index + 2,
      field: HEADERS.DOB,
      message: 'Missing date of birth',
    });
  }
  const date1Regex = /^\d{2}\/\d{2}\/\d{4}$/;
  const date2Regex = /^\d{2}-\d{2}-\d{4}$/;
  if (!date1Regex.test(date) && !date2Regex.test(date)) {
    errorsInvalid.push({
      line: index + 2,
      field: HEADERS.DOB,
      message: 'Invalid date of birth that must be dd/mm/yyyy or dd-mm-yyyy',
    });
  }
}

export function validateFirstName(
  index,
  firstName,
  errorMissing,
  errorInvalid,
) {
  if (!firstName) {
    errorMissing.push({
      line: index + 2,
      field: HEADERS.FIRST_NAME,
      message: 'Missing first name',
    });
  } else {
    const isNameRegex = /^[a-zA-Z\s]*$/;
    if (!isNameRegex.test(firstName)) {
      errorInvalid.push({
        line: index + 2,
        field: HEADERS.FIRST_NAME,
        message: 'Invalid first name',
      });
    }
  }
}

export function validateLastName(index, lastName, errorMissing, errorInvalid) {
  if (!lastName) {
    errorMissing.push({
      line: index + 2,
      field: HEADERS.LAST_NAME,
      message: 'Missing last name',
    });
  } else {
    const isNameRegex = /^[a-zA-Z\s]*$/;
    if (!isNameRegex.test(lastName)) {
      errorInvalid.push({
        line: index + 2,
        field: HEADERS.LAST_NAME,
        message: 'Invalid last name',
      });
    }
  }
}

export function checkCorrectHeaders(fileHeaders: any) {
  const headers = Object.values(HEADERS);

  return fileHeaders.every((fileHeader) => headers.includes(fileHeader));
}

export const convertStayWarningLabel = (warning: string) => {
  switch (warning) {
    case NRMaximumStayDurationLevel.EXCEEDED:
      return NRMaximumStayDurationLevelLabel.EXCEEDED;
    case NRMaximumStayDurationLevel.APPROACHING:
      return NRMaximumStayDurationLevelLabel.APPROACHING;
    default:
      return '';
  }
};

export const getLevelForExport = (
  level: string,
  emailTemplateOption: number,
) => {
  let emailTemplateOptionStr = '';
  switch (emailTemplateOption) {
    case 1:
      emailTemplateOptionStr = '1st';
      break;
    case 2:
      emailTemplateOptionStr = '2nd';
      break;
    default:
      break;
  }
  switch (level) {
    case NightRegistrationWarningLevel.GOOD:
      return 'Good';
    case NightRegistrationWarningLevel.VERBAL:
      return `${emailTemplateOptionStr} notification`;
    case NightRegistrationWarningLevel.OFFICIAL:
      return `${emailTemplateOptionStr} official warning`;
    case NightRegistrationWarningLevel.REMOVAL:
      return 'Removal';
    default:
      return '';
  }
};

export const getGender = (gender) => {
  if (!gender) {
    return NightRegistrationGender.MALE;
  }
  if (gender === 'MALE' || gender === 'M') {
    return NightRegistrationGender.MALE;
  } else {
    return NightRegistrationGender.FEMALE;
  }
};

export const getEmailTemplateName = (emailTemplateOption, level) => {
  let fullLevelStr = level;
  if (level !== 'removal') {
    switch (emailTemplateOption) {
      case 1:
        fullLevelStr = `night_registration_1st_${level}`;
        break;
      case 2:
        fullLevelStr = `night_registration_2nd_${level}`;
        break;
      default:
        break;
    }
  } else {
    fullLevelStr = 'night_registration_removal';
  }
  return fullLevelStr;
};

export const getNightRegistrationPDFFileNameByLevel = (
  level,
  emailTemplateOption,
  warningDate,
  residentLastName,
  fullAddress,
) => {
  const levelStr = level.split('|')[1];

  let pdfFileName = `First Verbal Notification`;
  switch (levelStr) {
    case 'verbal':
      pdfFileName = 'Verbal Notification';
      break;
    case 'official':
      pdfFileName = 'Official Warning';
      break;
    case 'removal':
      pdfFileName = 'Removal';
      break;
  }
  if (levelStr !== 'removal') {
    switch (emailTemplateOption) {
      case 1:
        pdfFileName = `First ${pdfFileName}`;
        break;
      case 2:
        pdfFileName = `Second ${pdfFileName}`;
        break;
      default:
        break;
    }
  }
  return `${dayjs(warningDate).format(DATE_FORMAT_ABBREVIATION)}_${pdfFileName}_${residentLastName}_${fullAddress}.pdf`;
};

export const getFullEmailTemplateOption = (emailTemplateOption) => {
  switch (emailTemplateOption) {
    case 1:
      return 'First';
    case 2:
      return 'Second';
    default:
      return '';
  }
};

export const getEmailSubject = (
  emailTemplateOption,
  levelStr,
  warningDate,
  clientId,
  residentLastName,
  fullAddress,
) => {
  switch (levelStr) {
    case 'verbal':
      return `${getFullEmailTemplateOption(emailTemplateOption)} Verbal Notification ${dayjs(warningDate).format(DATE_FORMAT_ABBREVIATION)}_${clientId}, ${residentLastName}, ${fullAddress}`;
    case 'official':
      return `${getFullEmailTemplateOption(emailTemplateOption)} Official Warning ${dayjs(warningDate).format(DATE_FORMAT_ABBREVIATION)}_${clientId}, ${residentLastName}, ${fullAddress}`;
    case 'removal':
      return `Removal ->  ${fullAddress}, ${dayjs(warningDate).format(DATE_FORMAT_ABBREVIATION)}_${clientId}, ${residentLastName}`;
    default:
      return '';
  }
};
