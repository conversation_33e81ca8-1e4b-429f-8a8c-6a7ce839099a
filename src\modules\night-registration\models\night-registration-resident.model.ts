import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { ContactDocument, ContactModel } from '~/modules/contact/contact.model';
import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { NightRegistrationGender } from '~/shared/enums/night-registration.enum';
import { BaseModel } from '~/shared/models/base.model';

import {
  NightRegistrationNationalityDocument,
  NightRegistrationNationalityModel,
} from './night-registration-nationality.model';

export type NightRegistrationResidentDocument =
  DocumentType<NightRegistrationResidentModel>;

@modelOptions({
  options: { customName: 'NightRegistrationResident' },
})
@index({ displayName: 1, dateOfBirth: 1 })
@index({ nationality: 1 })
@index({ contact: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class NightRegistrationResidentModel extends BaseModel {
  @prop({ required: true })
  firstName!: string;

  @prop({ required: true })
  lastName!: string;

  @prop({ required: true })
  displayName!: string;

  @prop({ required: true })
  dateOfBirth!: Date;

  @prop({ required: true, enum: NightRegistrationGender })
  gender!: NightRegistrationGender;

  @prop()
  clientId?: string;

  @prop({ email: true })
  email?: string;

  @prop({ ref: () => NightRegistrationNationalityModel })
  nationality?: Ref<NightRegistrationNationalityDocument>;

  @prop()
  phoneNumber?: string;

  @prop({ ref: () => ContactModel })
  contact?: Ref<ContactDocument>;
}
