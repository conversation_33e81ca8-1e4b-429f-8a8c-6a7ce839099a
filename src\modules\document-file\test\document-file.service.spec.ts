import { HttpService } from '@nestjs/axios';
import { ForbiddenException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { ObjectId } from 'mongodb';
import { of } from 'rxjs';

import { LocationService } from '~/modules/location/location.service';
import { TenantUserService } from '~/modules/tenant-user/tenant-user.service';
import { DocumentFileTypeEnum } from '~/shared/enums/document-file-type.enum';
import { DOCUMENT_FILE_MESSAGE_KEYS } from '~/shared/message-keys/document-file.message-keys';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import {
  initMockDocumentFile,
  mockDocumentFileData,
} from '~/test/mocks/documentfile.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import {
  initMockUploadFile,
  mockUploadFileData,
} from '~/test/mocks/uploadfile.mock';

import { DocumentFileModel } from '../document-file.model';
import { DocumentFileService } from '../document-file.service';
import {
  DocumentFileCheckExistedParamsDto,
  DocumentFileCreateDto,
  DocumentFileQueryParamDto,
} from '../dtos/docment-file.dto';
import { UploadFileModel } from '../upload-file.model';
import { documentFileTest } from './document-file.dto.test';

describe('DocumentFileService', () => {
  let service: DocumentFileService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        DocumentFileService,
        ...testInjectProviders([
          DocumentFileModel,
          UploadFileModel,
          LocationService,
          TenantUserService,
          HttpService,
        ]),
      ],
    }).compile();

    service = module.get(DocumentFileService);

    // Init mock data
    await Promise.all([
      initMockDocumentFile(),
      initMockUploadFile(),
      initMockLocation(),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('createDocumentFile', () => {
    const payload: DocumentFileCreateDto = {
      type: DocumentFileTypeEnum.NIGHT_REGISTRATIONS,
      uploadFileIds: [mockUploadFileData._id],
      uploaderName: 'Test Uploader',
      createdBy: new ObjectId(),
      location: mockLocationData._id,
    };

    it('should throw error when duplicate upload files', async () => {
      // Mock private methods
      jest
        .spyOn(service as any, 'validatePermissionDocumentFile')
        .mockResolvedValue(null);

      const wrongPayload = {
        ...payload,
        uploadFileIds: [mockUploadFileData._id, mockUploadFileData._id],
      };

      await expect(service.createDocumentFile(wrongPayload)).rejects.toThrow(
        DOCUMENT_FILE_MESSAGE_KEYS.DUPLICATE_UPLOAD_FILE,
      );
    });

    it('should throw error when type invalid', async () => {
      // Mock private methods
      jest
        .spyOn(service as any, 'validatePermissionDocumentFile')
        .mockResolvedValue(null);

      await expect(
        service.createDocumentFile({ ...payload, type: '' } as any),
      ).rejects.toThrow(DOCUMENT_FILE_MESSAGE_KEYS.CREATED_INVALID_TYPE);
    });

    it('should throw error when location is provided for non night registration type', async () => {
      // Mock private methods
      jest
        .spyOn(service as any, 'validatePermissionDocumentFile')
        .mockResolvedValue(null);

      const wrongPayload = {
        ...payload,
        type: DocumentFileTypeEnum.SUPPORT,
      };

      await expect(service.createDocumentFile(wrongPayload)).rejects.toThrow(
        DOCUMENT_FILE_MESSAGE_KEYS.LOCATION_ONLY_FOR_TYPE_NIGTH_REGISTRATION,
      );
    });

    it('should throw error when night registration type is missing location', async () => {
      // Mock private methods
      jest
        .spyOn(service as any, 'validatePermissionDocumentFile')
        .mockResolvedValue(null);

      const wrongPayload = {
        ...payload,
        location: undefined,
      };

      await expect(service.createDocumentFile(wrongPayload)).rejects.toThrow(
        DOCUMENT_FILE_MESSAGE_KEYS.TYPE_NIGTH_REGISTRATION_MUST_HAVE_LOCATION,
      );
    });

    it('should throw error when upload files are not found', async () => {
      // Mock private methods
      jest
        .spyOn(service as any, 'validatePermissionDocumentFile')
        .mockResolvedValue(null);
      jest.spyOn(service['uploadFileModel'], 'find').mockResolvedValue([]);

      const wrongPayload = {
        ...payload,
        uploadFileIds: [new ObjectId()],
      };

      await expect(service.createDocumentFile(wrongPayload)).rejects.toThrow(
        DOCUMENT_FILE_MESSAGE_KEYS.NOT_FOUND_ANY_UPLOAD_FILE,
      );
    });

    it('should call fn with payload and create data', async () => {
      // Mock private methods
      jest
        .spyOn(service as any, 'validatePermissionDocumentFile')
        .mockResolvedValue(null);
      jest.spyOn(service['locationService'], 'findOne').mockResolvedValue({});

      const result = await service.createDocumentFile(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(documentFileTest.createDocumentFileSchema);
    });

    it('should create and return data with location in payload', async () => {
      // Mock private methods
      jest
        .spyOn(service as any, 'validatePermissionDocumentFile')
        .mockResolvedValue(null);
      jest
        .spyOn(service['locationService'], 'findOne')
        .mockResolvedValue(mockLocationData);

      const result = await service.createDocumentFile(payload);
      expect(result).toBeDefined();
      expect(result[0]).toHaveProperty('location');
      expect(result[0]).toHaveProperty('location._id', mockLocationData._id);
      expect(result).toMatchSchema(documentFileTest.createDocumentFileSchema);
    });
  });

  describe('getListDocumentFile', () => {
    it('should call fn with payload and return list data', async () => {
      const payload: DocumentFileQueryParamDto = {
        type: DocumentFileTypeEnum.NIGHT_REGISTRATIONS,
      };

      const result = await service.getListDocumentFile(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(
        documentFileTest.getListDocumentFileSchema,
      );
    });

    it('should call fn and return list empty when data not exist', async () => {
      const payload: DocumentFileQueryParamDto = {
        type: DocumentFileTypeEnum.NIGHT_REGISTRATIONS,
        pageIndex: 99,
      };

      const result = await service.getListDocumentFile(payload);
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(0);
    });
  });

  describe('deleteDocumentFile', () => {
    it('should throw error when document file not found', async () => {
      const wrongId = new ObjectId().toString();

      await expect(
        service.deleteDocumentFile(wrongId, {}, wrongId),
      ).rejects.toThrow(DOCUMENT_FILE_MESSAGE_KEYS.NOT_FOUND_DOCUMENT_FILE);
    });

    it('should throw error when user not have permission', async () => {
      // Mock private methods
      jest
        .spyOn(service as any, 'validatePermissionDocumentFile')
        .mockRejectedValue(new ForbiddenException());

      const documentFileId = new ObjectId().toString();
      await initMockDocumentFile({ _id: documentFileId });

      await expect(
        service.deleteDocumentFile(documentFileId, {}, documentFileId),
      ).rejects.toThrow();
    });

    it('should call fn with payload and delete data', async () => {
      // Mock private methods
      jest
        .spyOn(service as any, 'validatePermissionDocumentFile')
        .mockResolvedValue(null);

      // Prepare data to delete
      const documentFileId = new ObjectId().toString();
      await initMockDocumentFile({ _id: documentFileId });

      jest
        .spyOn(service['httpService'], 'delete')
        .mockReturnValue(of({ data: {} }) as any);

      const result = await service.deleteDocumentFile(
        documentFileId,
        {},
        documentFileId,
      );

      expect(result).toBeDefined();
      expect(result.acknowledged).toBe(true);
    });
  });

  describe('checkExistedDocumentFile', () => {
    const payload: DocumentFileCheckExistedParamsDto = {
      fileName: mockDocumentFileData.fileName,
      type: DocumentFileTypeEnum.NIGHT_REGISTRATIONS,
      user: new ObjectId().toString(),
    };

    it('should return true when document file exists', async () => {
      // Mock private methods
      jest
        .spyOn(service as any, 'validatePermissionDocumentFile')
        .mockResolvedValue(null);

      const result = await service.checkExistedDocumentFile(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('isExisted', true);
    });

    it('should return false when document file not exist', async () => {
      // Mock private methods
      jest
        .spyOn(service as any, 'validatePermissionDocumentFile')
        .mockResolvedValue(null);

      const result = await service.checkExistedDocumentFile({
        ...payload,
        fileName: 'non-existing-file.txt',
      });
      expect(result).toBeDefined();
      expect(result).toHaveProperty('isExisted', false);
    });
  });
});
