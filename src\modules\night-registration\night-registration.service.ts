import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientProxy } from '@nestjs/microservices';
import { stringify } from 'csv-stringify';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import _, { omit, template } from 'lodash';
import { ObjectId } from 'mongodb';
import { Model } from 'mongoose';
import { nanoid } from 'nanoid';
import { firstValueFrom } from 'rxjs';
import * as XLSX from 'xlsx';

import {
  DATE_FORMAT_HYPHEN,
  DATE_FORMAT_SLASH,
  PDF_SERVICE_CLIENT,
} from '~/constants/app.constant';
import { EmailService } from '~/processors/email/email.service';
import { QueryParamsDto } from '~/shared/dtos/query-builder.dto';
import {
  AgreementLinePeriodType,
  ContractType,
} from '~/shared/enums/contract.enum';
import {
  JobCheckInCheckOutEnum,
  NightRegistrationWarningLevel,
  NRMaximumStayDurationLevel,
} from '~/shared/enums/night-registration.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { NIGHT_REGISTRATION_MESSAGE_KEYS } from '~/shared/message-keys/night-registration.message-keys';
import { NIGHT_REGISTRATION_MESSAGES } from '~/shared/messages/night-registration.message';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery, NATIONALITY_NO_CODE } from '~/utils';
import { formatDate } from '~/utils/date.util';

import { ContactModel } from '../contact/contact.model';
import { ContractModel } from '../contract/contract.model';
import { EmailTemplateModel } from '../email-template/email-template.model';
import { LocationModel } from '../location/location.model';
import { UnitModel } from '../unit/unit.model';
import {
  GetNightRegistrationReportQueryDto,
  ReservationCreateBodyDto,
  ReservationQueryParamsDto,
  ReservationUpdateBodyDto,
  ResidentHasExceededMaximumLengthOfStayDto,
  VirtualReservationCreateBodyDto,
  WarningCreateBodyDto,
  WarningQueryParamsDto,
} from './dtos/night-registration.dto';
import { NightRegistrationNationalityModel } from './models/night-registration-nationality.model';
import { NightRegistrationReservationModel } from './models/night-registration-reservation.model';
import { NightRegistrationResidentModel } from './models/night-registration-resident.model';
import { NightRegistrationWarningModel } from './models/night-registration-warning.model';
import { NightRegistrationWarningCategoryModel } from './models/night-registration-warning-category';
import {
  checkCorrectHeaders,
  convertStayWarningLabel,
  getEmailSubject,
  getEmailTemplateName,
  getGender,
  getLevelForExport,
  getNightRegistrationPDFFileNameByLevel,
  HEADERS,
  validateClientId,
  validateDateOfBirthFormat,
  validateEmail,
  validateFirstName,
  validateGender,
  validateLastName,
  validatePhoneNumber,
} from './night-registration.util';

@Injectable()
export class NightRegistrationService {
  private readonly logger = new Logger(NightRegistrationService.name);
  constructor(
    @InjectModel(LocationModel)
    private readonly locationModel: Model<LocationModel>,
    @InjectModel(ContractModel)
    private readonly contractModel: Model<ContractModel>,
    @InjectModel(NightRegistrationReservationModel)
    private readonly reservationModel: Model<NightRegistrationReservationModel>,
    @InjectModel(NightRegistrationResidentModel)
    private readonly residentModel: MongooseModel<NightRegistrationResidentModel>,
    @InjectModel(UnitModel)
    private readonly unitModel: Model<UnitModel>,
    @InjectModel(NightRegistrationWarningCategoryModel)
    private readonly warningCategoryModel: MongooseModel<NightRegistrationWarningCategoryModel>,
    @InjectModel(NightRegistrationWarningModel)
    private readonly warningModel: MongooseModel<NightRegistrationWarningModel>,
    @InjectModel(EmailTemplateModel)
    private readonly emailTemplateModel: MongooseModel<EmailTemplateModel>,
    @InjectModel(ContactModel)
    private readonly contactModel: MongooseModel<ContactModel>,
    @InjectModel(NightRegistrationNationalityModel)
    private readonly nationalityModel: MongooseModel<NightRegistrationNationalityModel>,

    private readonly emailService: EmailService,
    private readonly configService: ConfigService,
    @Inject(PDF_SERVICE_CLIENT)
    private readonly pdfClient: ClientProxy,
  ) {}

  async getListReservation(params: ReservationQueryParamsDto) {
    const { location, checkOut, contact, maximumStayDurationLevel, ...rest } =
      params;
    const locationInfos = await this.locationModel
      .aggregate([
        {
          $match: {
            _id: new ObjectId(location),
          },
        },
        {
          $lookup: {
            from: 'units',
            localField: '_id',
            foreignField: 'location',
            as: 'units',
            pipeline: [
              {
                $match: {
                  isActive: true,
                },
              },
              {
                $lookup: {
                  from: 'units',
                  localField: 'parent',
                  foreignField: '_id',
                  as: 'parent',
                },
              },
              {
                $set: {
                  parent: {
                    $arrayElemAt: ['$parent', 0],
                  },
                },
              },
              {
                $project: {
                  isRoot: 1,
                  name: 1,
                  maxOccupants: 1,
                  parent: 1,
                  position: 1,
                },
              },
            ],
          },
        },
        {
          $project: {
            units: 1,
            fullAddress: 1,
            costCenter: 1,
            maximumStayDuration: 1,
          },
        },
      ])
      .exec();
    const foundLocation = locationInfos[0];
    const foundContracts = await this.contractModel
      .aggregate([
        {
          $match: {
            isActive: true,
            type: {
              $in: [ContractType.RENTING, ContractType.SERVICE],
            },
            $or: [
              {
                location: foundLocation._id,
              },
              ...(foundLocation.costCenter
                ? [{ costCenter: foundLocation.costCenter }]
                : []),
            ],
            $and: [
              {
                startDate: {
                  $lte: dayjs().utc().toDate(),
                },
              },
              {
                $or: [
                  {
                    endDate: {
                      $gte: dayjs().utc().startOf('day').toDate(),
                    },
                  },
                  {
                    endDate: null,
                  },
                ],
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'agreementlines',
            localField: '_id',
            foreignField: 'contract',
            as: 'agreementlines',
            pipeline: [
              {
                $lookup: {
                  from: 'costlinegenerals',
                  localField: '_id',
                  foreignField: 'agreementLine',
                  as: 'costLineGenerals',
                  pipeline: [
                    {
                      $match: {
                        $and: [
                          {
                            unit: {
                              $exists: true,
                            },
                          },
                          {
                            startDate: {
                              $lte: dayjs().utc().toDate(),
                            },
                          },
                          {
                            $or: [
                              {
                                endDate: {
                                  $gte: dayjs().utc().startOf('day').toDate(),
                                },
                              },
                              {
                                endDate: null,
                              },
                            ],
                          },
                        ],
                      },
                    },
                    {
                      $lookup: {
                        from: 'units',
                        localField: 'unit',
                        foreignField: '_id',
                        as: 'unit',
                        pipeline: [
                          {
                            $match: {
                              isActive: true,
                            },
                          },
                        ],
                      },
                    },
                    {
                      $unwind: {
                        path: '$unit',
                        preserveNullAndEmptyArrays: true,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'contacts',
            localField: 'contact',
            foreignField: '_id',
            as: 'contact',
            pipeline: [
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  displayName: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            contact: {
              $arrayElemAt: ['$contact', 0],
            },
          },
        },
      ])
      .option({ allowDiskUse: true })
      .exec();

    if (foundContracts.length === 0) {
      return [];
    }

    const unitOfLocations = foundLocation.units;

    const beds = await this.generateBedFromLocationAndContract(
      unitOfLocations,
      foundContracts,
    );

    let bedWithReservations = await this.mapReservationToBeds(
      beds,
      checkOut,
      foundLocation.maximumStayDuration,
    );
    if (checkOut === 'true') {
      // Filter agency/debtor contact
      bedWithReservations = bedWithReservations.filter((bed) => {
        return bed.reservation?._id;
      });
    }

    if (contact) {
      bedWithReservations = bedWithReservations.filter((bed) => {
        return (
          bed.reservation?.contact?._id.toString() === contact ||
          bed.contact?._id.toString() === contact
        );
      });
    }

    if (maximumStayDurationLevel) {
      bedWithReservations = bedWithReservations.filter((bed) => {
        return bed.reservation.maximumStayWarning === maximumStayDurationLevel;
      });
    }

    const generated: any[] = [];
    bedWithReservations.forEach((bed: any) => {
      if (generated.includes(bed.nanoid)) {
        return;
      }

      const gender = bed.reservation?.resident?.gender;
      if (gender === null) {
        bed.availableGender = gender;
        generated.push(bed.uuid);
        return;
      }

      bedWithReservations
        .filter((bedNeedToUpdateGender) => {
          return (
            bedNeedToUpdateGender.unitId === bed.unitId &&
            !bedNeedToUpdateGender?.reservation?._id &&
            !bedNeedToUpdateGender?.reservation?.isVirtual
          );
        })
        .forEach((bedNeedToUpdateGender) => {
          bedNeedToUpdateGender.availableGender = gender;
          generated.push(bedNeedToUpdateGender.nanoid);
        });
    });

    let totalAvailableBeds = 0;
    let totalReservedBeds = 0;
    let totalOccupiedBeds = 0;
    let totalOnlyForMaleBeds = 0;
    let totalOnlyForFemaleBeds = 0;
    bedWithReservations.forEach((bed) => {
      const reservation = bed.reservation;
      if (!reservation?._id && !reservation?.isVirtual) {
        totalAvailableBeds++;
      }

      if (reservation?._id && reservation?.isVirtual === true) {
        totalReservedBeds++;
      }
      if (reservation?.resident?._id) {
        totalOccupiedBeds++;
      }
      if (bed.availableGender === 'Male') {
        totalOnlyForMaleBeds++;
      }

      if (bed.availableGender === 'Female') {
        totalOnlyForFemaleBeds++;
      }
    });

    const { query, options } = buildQuery(rest);
    if (query.filter) {
      bedWithReservations = this.filterBedsWithReservations(
        bedWithReservations,
        params.filter,
      );
    }

    bedWithReservations = this.sortBedsWithReservations(
      bedWithReservations,
      options.sort,
    );

    return {
      totalAvailableBeds,
      totalReservedBeds,
      totalOccupiedBeds,
      totalOnlyForMaleBeds,
      totalOnlyForFemaleBeds,
      beds: bedWithReservations,
    };
  }

  async getContactsByLocation(location: string) {
    const foundLocation = await this.locationModel.findById(location).lean();

    if (!foundLocation) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.LOCATION_NOT_FOUND,
      );
    }

    const foundContracts = await this.contractModel
      .aggregate([
        {
          $match: {
            isActive: true,
            type: {
              $in: [ContractType.RENTING, ContractType.SERVICE],
            },
            $or: [
              {
                location: foundLocation._id,
              },
              ...(foundLocation.costCenter
                ? [{ costCenter: foundLocation.costCenter }]
                : []),
            ],
            $and: [
              {
                startDate: {
                  $lte: dayjs().utc().toDate(),
                },
              },
              {
                $or: [
                  {
                    endDate: {
                      $gte: dayjs().utc().startOf('day').toDate(),
                    },
                  },
                  {
                    endDate: null,
                  },
                ],
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'contacts',
            localField: 'contact',
            foreignField: '_id',
            as: 'contact',
            pipeline: [
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  displayName: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            contact: {
              $arrayElemAt: ['$contact', 0],
            },
          },
        },
      ])
      .exec();
    const datas = foundContracts.map((contract) => contract.contact);
    return _.uniqBy(datas, 'displayName');
  }

  async getListResidents(params: QueryParamsDto) {
    const { query, options } = buildQuery(params);
    let condition: any = {};
    if (params._q) {
      condition = {
        $or: [
          {
            $expr: {
              $regexMatch: {
                input: {
                  $dateToString: { format: '%d/%m/%Y', date: '$dateOfBirth' },
                },
                regex: params._q,
                options: 'i',
              },
            },
          },
          {
            displayName: {
              $regex: params._q,
              $options: 'i',
            },
          },
        ],
      };
    }
    return this.residentModel.aggregatePaginate(
      this.residentModel.aggregate([
        {
          $match: {
            ...query,
            ...condition,
          },
        },
        {
          $lookup: {
            from: 'contacts',
            localField: 'contact',
            foreignField: '_id',
            as: 'contact',
            pipeline: [
              {
                $project: {
                  displayName: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            contact: {
              $arrayElemAt: ['$contact', 0],
            },
          },
        },
        {
          $lookup: {
            from: 'nightregistrationnationalities',
            localField: 'nationality',
            foreignField: '_id',
            as: 'nationality',
            pipeline: [
              {
                $project: {
                  _id: 1,
                  name: 1,
                  nameDutch: 1,
                  code: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            nationality: {
              $arrayElemAt: ['$nationality', 0],
            },
          },
        },
        {
          $lookup: {
            from: 'nightregistrationwarnings',
            localField: '_id',
            foreignField: 'resident',
            as: 'warnings',
            pipeline: [
              {
                $sort: {
                  level: -1,
                  emailTemplateOption: -1,
                },
              },
              {
                $project: {
                  _id: 1,
                  level: 1,
                  emailTemplateOption: 1,
                },
              },
              {
                $limit: 1,
              },
            ],
          },
        },
        {
          $set: {
            warnings: {
              $arrayElemAt: ['$warnings', 0],
            },
          },
        },
        {
          $project: {
            firstName: 1,
            lastName: 1,
            gender: 1,
            dateOfBirth: 1,
            clientId: 1,
            phoneNumber: 1,
            email: 1,
            displayName: 1,
            fullInfomation: {
              $concat: [
                '$lastName',
                ', ',
                '$firstName',
                ' - ',
                {
                  $dateToString: { format: '%d/%m/%Y', date: '$dateOfBirth' },
                },
              ],
            },
            contact: 1,
            nationality: 1,
            level: {
              $ifNull: ['$warnings.level', NightRegistrationWarningLevel.GOOD],
            },
          },
        },
      ]),
      {
        offset: options.offset,
        limit: options.limit,
        sort: { _id: -1 },
        collation: options.collation,
        allowDiskUse: true,
      },
    );
  }

  async createVirtualReservation(payload: VirtualReservationCreateBodyDto) {
    this.logger.log(`Create virtual reservation ${JSON.stringify(payload)}`);
    const { unit, bed } = payload;
    const isExisted = await this.checkBedReserved(unit, bed, null);

    if (isExisted) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.BED_ALREADY_RESERVED,
      );
    }
    const foundUnit = await this.unitModel.findById(unit).lean();
    if (!foundUnit) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.UNIT_NOT_FOUND,
      );
    }

    if (bed > foundUnit.maxOccupants) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.BED_NOT_FOUND,
      );
    }

    const reservation = await this.reservationModel.create({
      ...payload,
      isVirtual: true,
    });

    return await this.findOne(reservation._id);
  }

  async createReservation(payload: ReservationCreateBodyDto) {
    this.logger.log(`Create reservation ${JSON.stringify(payload)}`);
    const { unit, bed, resident, companyInfomation, ...rest } = payload;
    const isExisted = await this.checkBedReserved(unit, bed, null);

    if (isExisted) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.BED_ALREADY_RESERVED,
      );
    }
    const foundUnit = await this.unitModel.findById(unit).lean();
    if (!foundUnit) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.UNIT_NOT_FOUND,
      );
    }

    const foundContacts = (
      await this.getContactsByLocation(foundUnit.location.toString())
    ).map((contact) => contact._id.toString());

    if (!foundContacts.includes(rest.contact)) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.CONTACT_NOT_FOUND,
      );
    }

    if (bed > foundUnit.maxOccupants) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.BED_NOT_FOUND,
      );
    }

    const foundResident = await this.createOrUpdateResident({
      ...resident,
      contact: rest.contact,
    });

    const reservation = await this.reservationModel.create({
      ...rest,
      unit,
      bed,
      resident: foundResident._id,
    });

    this.sendEmailToResident(
      foundResident,
      reservation,
      companyInfomation,
      foundUnit,
      true,
      true,
    )
      .then(() => {
        this.logger.log(`Start sent email to resident ${foundResident._id}`);
        //TODO will do it later
        this.logger.log(
          `Sent email to resident ${foundResident._id} successfully`,
        );
      })
      .catch((error) => {
        this.logger.error(
          `Error when sent email to resident ${foundResident._id}`,
          error,
        );
      });

    this.logger.log(`Created reservation ${reservation._id}`);

    return await this.findOne(reservation._id);
  }

  async deleteVirtualReservation(reservationId: string) {
    const foundReservation = await this.reservationModel
      .findById(reservationId)
      .lean();
    if (!foundReservation) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.RESERVATION_NOT_FOUND,
      );
    }

    if (!foundReservation.isVirtual) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.ONLY_DELETE_VIRTUAL_RESERVATION,
      );
    }

    return await this.reservationModel.deleteOne({ _id: reservationId });
  }

  async findOne(id: any) {
    const reservation = await this.reservationModel
      .aggregate([
        {
          $match: {
            _id: id,
          },
        },
        {
          $lookup: {
            from: 'nightregistrationresidents',
            localField: 'resident',
            foreignField: '_id',
            as: 'resident',
            pipeline: [
              {
                $lookup: {
                  from: 'nightregistrationnationalities',
                  localField: 'nationality',
                  foreignField: '_id',
                  as: 'nationality',
                  pipeline: [
                    {
                      $project: {
                        _id: 1,
                        name: 1,
                        nameDutch: 1,
                        code: 1,
                      },
                    },
                  ],
                },
              },
              {
                $set: {
                  nationality: {
                    $arrayElemAt: ['$nationality', 0],
                  },
                },
              },
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  gender: 1,
                  dateOfBirth: 1,
                  clientId: 1,
                  phoneNumber: 1,
                  email: 1,
                  nationality: 1,
                  fullInfomation: {
                    $concat: [
                      '$lastName',
                      ', ',
                      '$firstName',
                      ' - ',
                      {
                        $dateToString: {
                          format: '%d/%m/%Y',
                          date: '$dateOfBirth',
                        },
                      },
                    ],
                  },
                },
              },
            ],
          },
        },
        {
          $set: {
            resident: {
              $arrayElemAt: ['$resident', 0],
            },
          },
        },
        {
          $lookup: {
            from: 'nightregistrationwarnings',
            localField: '_id',
            foreignField: 'reservation',
            as: 'warnings',
            let: { residentId: '$resident._id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ['$resident', '$$residentId'],
                  },
                },
              },
              {
                $sort: {
                  level: -1,
                },
              },
              {
                $limit: 1,
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'contacts',
            localField: 'contact',
            foreignField: '_id',
            as: 'contact',
            pipeline: [
              {
                $project: {
                  displayName: 1,
                  identifier: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'units',
            localField: 'unit',
            foreignField: '_id',
            as: 'unit',
            pipeline: [
              {
                $project: {
                  name: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            unit: {
              $arrayElemAt: ['$unit', 0],
            },
          },
        },
        {
          $project: {
            _id: 1,
            resident: 1,
            unit: 1,
            remarks: 1,
            bed: 1,
            arrivalDate: 1,
            departureDate: 1,
            isVirtual: 1,
          },
        },
        {
          $set: {
            contact: {
              $arrayElemAt: ['$contact', 0],
            },
          },
        },
        {
          $sort: {
            isVirtual: -1,
            arrivalDate: -1,
            createdAt: -1,
          },
        },
        {
          $limit: 1,
        },
      ])
      .exec();

    if (
      reservation[0]._id &&
      reservation[0].resident &&
      reservation[0].warnings &&
      reservation[0].warnings.length > 0
    ) {
      reservation[0] = {
        ...reservation[0],
        level: reservation[0].warnings[0].level,
      };
    }

    if (
      reservation[0]._id &&
      reservation[0].resident &&
      ((reservation[0].warnings && reservation[0].warnings.length === 0) ||
        !reservation[0].warnings)
    ) {
      reservation[0] = {
        ...reservation[0],
        level: NightRegistrationWarningLevel.GOOD,
      };
    }

    //Remove list warnings
    delete reservation[0].warnings;
    return reservation[0];
  }

  async updateReservation(payload: ReservationUpdateBodyDto) {
    this.logger.log(`Update reservation ${JSON.stringify(payload)}`);
    const { _id, resident, bed, unit, companyInfomation, ...rest } = payload;
    const foundReservation = await this.reservationModel.findById(_id).lean();

    if (!foundReservation) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.RESERVATION_NOT_FOUND,
      );
    }

    const foundUnit = await this.unitModel.findById(unit).lean();
    if (!foundUnit) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.UNIT_NOT_FOUND,
      );
    }

    const foundContacts = (
      await this.getContactsByLocation(foundUnit.location.toString())
    ).map((contact) => contact._id.toString());

    if (!foundContacts.includes(rest.contact)) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.CONTACT_NOT_FOUND,
      );
    }

    if (bed > foundUnit.maxOccupants) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.BED_NOT_FOUND,
      );
    }

    const foundResident = await this.createOrUpdateResident({
      ...resident,
      contact: rest.contact,
    });

    if (
      foundReservation.unit.toString() != unit ||
      foundReservation.bed != bed
    ) {
      const isExisted = await this.checkBedReserved(
        unit,
        bed,
        foundReservation._id,
      );

      if (isExisted) {
        throw new BadRequestException(
          NIGHT_REGISTRATION_MESSAGE_KEYS.BED_ALREADY_RESERVED,
        );
      }
    }

    const updatedReservation = await this.reservationModel.findByIdAndUpdate(
      _id,
      {
        ...rest,
        unit,
        bed,
        resident: foundResident._id,
        isVirtual: false,
      },
      { new: true },
    );

    const canSentArrivalEmail =
      !dayjs(rest.arrivalDate)
        .utc()
        .startOf('day')
        .isSame(dayjs(foundReservation.arrivalDate).utc().startOf('day')) ||
      foundReservation.unit.toString() != unit ||
      foundReservation.bed != bed;
    const canSentDepartureEmail =
      (rest.departureDate &&
        foundReservation.departureDate &&
        !dayjs(rest.departureDate)
          .utc()
          .startOf('day')
          .isSame(
            dayjs(foundReservation.departureDate).utc().startOf('day'),
          )) ||
      (rest.departureDate && !foundReservation.departureDate) ||
      foundReservation.unit.toString() != unit ||
      foundReservation.bed != bed;
    const isChangeInfomation =
      foundResident.email !== resident.email ||
      canSentArrivalEmail ||
      canSentDepartureEmail;
    if (isChangeInfomation) {
      this.sendEmailToResident(
        foundResident,
        updatedReservation,
        companyInfomation,
        foundUnit,
        canSentArrivalEmail,
        canSentDepartureEmail,
      )
        .then(() => {
          this.logger.log(`Start sent email to resident ${foundResident._id}`);
          //TODO will do it later
          this.logger.log(
            `Sent email to resident ${foundResident._id} successfully`,
          );
        })
        .catch((error) => {
          this.logger.error(
            `Error when sent email to resident ${foundResident._id}`,
            error,
          );
        });
    }

    this.logger.log(
      `Updated reservation ${updatedReservation?._id} successfully!`,
    );
    return await this.findOne(foundReservation._id);
  }

  async deleteReservation(reservationId: string) {
    const foundReservation = await this.reservationModel
      .findById(reservationId)
      .lean();
    if (!foundReservation) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.RESERVATION_NOT_FOUND,
      );
    }

    return await this.reservationModel.deleteOne({ _id: reservationId });
  }

  async getWarningCategories(payload: any) {
    const { query, options } = buildQuery(payload, ['category']);
    const finalOptions =
      !payload.pageSize || options.limit === -1
        ? { ...omit(options, ['limit']), pagination: false }
        : options;
    return await this.warningCategoryModel.paginate(query, {
      ...finalOptions,
      sort: { identifier: 1 },
      projection: {
        category: 1,
      },
    });
  }

  async createWarning(payload: WarningCreateBodyDto) {
    this.logger.log(`Create warning ${JSON.stringify(payload)}`);
    const foundReservation = await this.reservationModel
      .findById(payload.reservation)
      .populate([
        {
          path: 'contact',
          select: 'displayName warningEmail',
        },
      ])
      .lean();
    if (!foundReservation) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.RESERVATION_NOT_FOUND,
      );
    }

    if (
      !foundReservation.resident ||
      payload.resident !== foundReservation.resident.toString()
    ) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.RESIDENT_NOT_FOUND,
      );
    }

    const foundWarningCategory = await this.warningCategoryModel
      .findById(payload.warningCategory)
      .lean();

    if (!foundWarningCategory) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.WARNING_CATEGORY_NOT_FOUND,
      );
    }

    const { companyInfomation, ...rest } = payload;
    const foundWarning = await this.warningModel.create({
      ...rest,
    });
    try {
      this.logger.log(
        `Start generate PDF for warning ${foundWarning._id?.toString()}`,
      );
      firstValueFrom(
        this.pdfClient.send(
          { cmd: NIGHT_REGISTRATION_MESSAGES.NR_GENERATE_WARNING_PDF },
          {
            id: foundWarning._id,
            headers: payload.headers,
            companyInfomation,
          },
        ),
      )
        .then((response) => {
          const { publicPdfUrl } = response;
          this.logger.log(
            `Start sent email to contact ${foundReservation.contact?._id?.toString()}`,
          );
          this.sendEmailWarningToContact(
            foundReservation,
            foundWarning,
            foundWarningCategory,
            companyInfomation,
            publicPdfUrl,
          )
            .then(() => {
              this.logger.log(
                `Sent email to contact ${foundReservation.contact?._id?.toString()} successfully`,
              );
            })
            .catch((error) => {
              this.logger.error(
                `Error when sent email to contact ${foundReservation.contact?._id?.toString()}`,
                error,
              );
            });
        })
        .catch((error) => {
          this.logger.error(
            `Error when PDF for warning ${foundWarning._id?.toString()}`,
            error,
          );
        });
    } catch (error) {
      console.log(error);
      throw error;
    }
    return foundWarning;
  }

  async getWarnings(payload: WarningQueryParamsDto) {
    const foundResident = await this.residentModel
      .findById(payload.resident)
      .lean();

    if (!foundResident) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.RESIDENT_NOT_FOUND,
      );
    }

    const warnings = await this.warningModel
      .find({
        resident: foundResident._id,
      })
      .populate([
        {
          path: 'warningCategory',
          select: 'category',
        },
      ])
      .sort({ createdAt: -1 })
      .select(
        'dayToLeave level description warningDate warningTime images emailTemplateOption warningCategory createdAt',
      )
      .lean();
    return warnings.map((warning) => {
      return {
        ...warning,
        warningTime: warning.warningTime ?? '00:00',
      };
    });
  }

  async deleteWarning(warningId: string) {
    const deletedWarning = await this.warningModel.findByIdAndDelete(warningId);
    if (!deletedWarning) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.WARNING_NOT_FOUND,
      );
    }
    return deletedWarning;
  }

  async importResidents(file: Express.Multer.File) {
    const buffer = Buffer.from(file.buffer);
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const workSheet = workbook.Sheets[workbook.SheetNames[0]];
    let datas = XLSX.utils.sheet_to_json(workSheet, {
      blankrows: true,
    });
    if (!datas || datas.length === 0) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.SHEET_MUST_HAVE_DATA,
      );
    }
    const isValidHeader = checkCorrectHeaders(Object.keys(datas[0] as any));
    if (!isValidHeader) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.INVALID_HEADERS,
      );
    }

    for (let i = datas.length - 1; i >= 0; i--) {
      if (
        Object.values(datas[i] as any).every((field) => field === undefined)
      ) {
        datas.pop();
      } else {
        break;
      }
    }

    const excelDateToJSDate = (excelDate) =>
      new Date((excelDate - 25569) * 86400 * 1000);
    datas = datas.map((data: any) => {
      return {
        ...data,
        [HEADERS.DOB]: data[HEADERS.DOB]
          ? typeof data[HEADERS.DOB] === 'number'
            ? dayjs(excelDateToJSDate(data[HEADERS.DOB])).format(
                DATE_FORMAT_SLASH,
              )
            : data[HEADERS.DOB]
          : undefined,
        [HEADERS.NATIONALITY]: data[HEADERS.NATIONALITY] ?? 'NOCODE', // Automatically assign 'NOCODE' if undefined
      };
    });

    const customerDatas = datas
      .map((data: any) => data[HEADERS.CUSTOMER])
      .filter((data) => data);

    const foundCustomers = await this.contactModel
      .find({
        displayName: { $in: customerDatas },
        isActive: true,
      })
      .lean();

    // get all nationalities
    const nationalitiesData = datas
      .map((data: any) => data[HEADERS.NATIONALITY])
      .filter((data) => data);

    const foundNationalities = await this.nationalityModel
      .find({
        code: { $in: nationalitiesData },
      })
      .lean();

    const { errorsMissing, errorsInvalid } = await this.validateImportData(
      datas,
      foundCustomers,
      foundNationalities,
    );
    if (errorsMissing.length > 0 || errorsInvalid.length > 0) {
      throw new BadRequestException({
        message: NIGHT_REGISTRATION_MESSAGE_KEYS.INVALID_DATA,
        errors: [...errorsMissing, ...errorsInvalid],
      });
    }

    dayjs.extend(customParseFormat);
    const convertedDatas = datas.map((data: any) => {
      return {
        firstName: data[HEADERS.FIRST_NAME],
        lastName: data[HEADERS.LAST_NAME],
        dateOfBirth: dayjs(data[HEADERS.DOB], DATE_FORMAT_SLASH)
          .utc()
          .startOf('day')
          .toDate(),
        gender: getGender(data[HEADERS.GENDER]),
        clientId: data[HEADERS.CLIENT_ID],
        phoneNumber: data[HEADERS.PHONE_NUM],
        email: data[HEADERS.EMAIL],
        nationality: data[HEADERS.NATIONALITY]
          ? foundNationalities.find(
              (value) => value.code === data[HEADERS.NATIONALITY],
            )?._id
          : this.getNationalityNoCode(true),
        contact: data[HEADERS.CUSTOMER]
          ? foundCustomers.find(
              (value) => value.displayName === data[HEADERS.CUSTOMER],
            )?._id
          : undefined,
      };
    });
    const promises = convertedDatas.map(async (data) => {
      return await this.createOrUpdateResident(data);
    });
    return await Promise.all(promises);
  }

  async exportReservation(params: ReservationQueryParamsDto) {
    const location = await this.locationModel.findById(params.location).lean();
    if (!location) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.LOCATION_NOT_FOUND,
      );
    }
    const currentDate = dayjs().utc().format(DATE_FORMAT_HYPHEN);
    const fileName = `night-registration-report-${currentDate}.csv`;
    const csvLines: any[] = [];
    csvLines.push([
      'Location',
      'Unit',
      'Kamer/bed',
      'Gereserveerd',
      'Aankomstdatum',
      'Vertrekdatum',
      'Voornaam',
      'Achternaam',
      'Geboortedatum',
      'Geslacht',
      'Klant',
      'Client ID',
      'Telefoonnummer',
      'E-mailadres',
      'Nationaliteit',
      'Opmerking',
      'Max verblijfsduur',
      'Controle',
      'Uiterste vertrekdatum',
    ]);
    const reservationDatas: any = await this.getListReservation(params);
    if (reservationDatas && reservationDatas?.beds?.length > 0) {
      const beds = reservationDatas.beds;
      beds.forEach((bed) => {
        const reservation = bed.reservation;
        const resident = reservation?.resident;
        const contact = bed.contact;
        let dayToLeave = '';
        if (resident?.level === '3|removal') {
          dayToLeave = resident?.dayToLeave
            ? dayjs(resident?.dayToLeave).utc().format(DATE_FORMAT_SLASH)
            : '';
        }
        csvLines.push([
          location.fullAddress,
          bed.unitName,
          bed.bedNoWithName,
          reservation
            ? reservation._id && reservation.isVirtual
              ? 'x'
              : ''
            : '',
          reservation?.arrivalDate
            ? dayjs(reservation?.arrivalDate).utc().format(DATE_FORMAT_SLASH)
            : '',
          reservation?.departureDate
            ? dayjs(reservation?.departureDate).utc().format(DATE_FORMAT_SLASH)
            : '',
          resident?.firstName ?? '',
          resident?.lastName ?? '',
          resident?.dateOfBirth
            ? dayjs(resident.dateOfBirth).utc().format(DATE_FORMAT_SLASH)
            : '',
          resident?.gender ?? '',
          reservation && reservation?.contact
            ? reservation?.contact?.displayName
            : contact
              ? contact.displayName
              : '',
          resident?.clientId ?? '',
          resident?.phoneNumber ?? '',
          resident?.email ?? '',
          resident?.nationality?.nameDutch ?? 'Onbekend',
          reservation?.remarks ?? '',
          convertStayWarningLabel(reservation?.maximumStayWarning ?? ''),
          getLevelForExport(
            reservation?.level ? reservation?.level : resident?.level,
            reservation?.emailTemplateOption,
          ),
          dayToLeave,
        ]);
      });
    }

    const content = await new Promise((resolve, reject) => {
      stringify(csvLines, { delimiter: ';' }, (err, output) => {
        if (err) reject(err);
        else resolve(output);
      });
    });

    return { fileName, content };
  }

  async CronJobSendMailResident(params: any) {
    const { companyInfomation } = params;

    const today = {
      start: dayjs().utc().startOf('day').toDate(),
      end: dayjs().utc().endOf('day').toDate(),
    };
    const tomorrow = {
      start: dayjs(today.start).add(1, 'day').toDate(),
      end: dayjs(today.start).add(1, 'day').endOf('day').toDate(),
    };

    const reservations = await this.reservationModel.aggregate([
      {
        $match: {
          isDeleted: false,
          $or: [
            { arrivalDate: { $gte: today.start, $lt: today.end } },
            { departureDate: { $gte: today.start, $lt: today.end } },
            { departureDate: { $gte: tomorrow.start, $lt: tomorrow.end } },
          ],
        },
      },
      {
        $lookup: {
          from: 'nightregistrationresidents',
          localField: 'resident',
          foreignField: '_id',
          as: 'resident',
        },
      },
      { $unwind: { path: '$resident', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'units',
          localField: 'unit',
          foreignField: '_id',
          as: 'unit',
        },
      },
      { $unwind: { path: '$unit', preserveNullAndEmptyArrays: true } },
    ]);

    for (const reservation of reservations) {
      this.sendEmailToResident(
        reservation.resident,
        reservation,
        companyInfomation,
        reservation.unit,
      )
        .then(() => {
          this.logger.log(
            `Start sent email to resident ${reservation.resident._id}`,
          );
          //TODO will do it later
          this.logger.log(
            `Sent email to resident ${reservation.resident._id} successfully`,
          );
        })
        .catch((error) => {
          this.logger.error(
            `Error when sent email to resident ${reservation.resident._id}`,
            error,
          );
        });
    }
  }

  async getLastCheckInAndLastCheckOutOfUnit(unitId, jobId) {
    const foundUnit = await this.unitModel.findById(unitId).lean();
    if (!foundUnit) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.UNIT_NOT_FOUND,
      );
    }
    const result: any[] = [];
    const subUnits = await this.unitModel.exists({ parent: foundUnit._id });
    if (foundUnit.isRoot || subUnits) {
      return result;
    }
    const currentDate = dayjs().utc().toDate();
    const lastCheckIns = await this.reservationModel
      .aggregate([
        {
          $match: {
            unit: unitId,
            $and: [
              {
                arrivalDate: {
                  $lte: currentDate,
                },
              },
              {
                $or: [
                  {
                    departureDate: { $gt: currentDate },
                  },
                  {
                    departureDate: null,
                  },
                ],
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'nightregistrationresidents',
            localField: 'resident',
            foreignField: '_id',
            as: 'resident',
            pipeline: [
              {
                $project: {
                  _id: 1,
                  fullInfomation: {
                    $concat: ['$displayName', ', ', '$clientId'],
                  },
                },
              },
            ],
          },
        },
        {
          $set: {
            resident: {
              $arrayElemAt: ['$resident', 0],
            },
          },
        },
        {
          $sort: {
            arrivalDate: -1,
            createdAt: -1,
          },
        },
        {
          $limit: 9999,
        },
      ])
      .exec();
    const lastCheckOuts = await this.reservationModel
      .aggregate([
        {
          $match: {
            unit: unitId,
            departureDate: {
              $lte: currentDate,
            },
          },
        },
        {
          $lookup: {
            from: 'nightregistrationresidents',
            localField: 'resident',
            foreignField: '_id',
            as: 'resident',
            pipeline: [
              {
                $project: {
                  _id: 1,
                  fullInfomation: {
                    $concat: ['$displayName', ', ', '$clientId'],
                  },
                },
              },
            ],
          },
        },
        {
          $set: {
            resident: {
              $arrayElemAt: ['$resident', 0],
            },
          },
        },
        {
          $sort: {
            departureDate: -1,
            createdAt: -1,
          },
        },
        {
          $limit: 9999,
        },
      ])
      .exec();
    if (lastCheckIns.length > 0 || lastCheckOuts.length > 0) {
      for (let i = 1; i <= foundUnit.maxOccupants; i++) {
        const lastCheckIn = lastCheckIns.find((checkIn) => checkIn.bed === i);
        const lastCheckOut = lastCheckOuts.find(
          (checkOut) => checkOut.bed === i,
        );
        result.push({
          bed: `${foundUnit.name},${i}`,
          lastCheckIn: lastCheckIn
            ? {
                residentId: lastCheckIn.resident?._id,
                resident: lastCheckIn.resident?.fullInfomation,
                reservation: lastCheckIn._id,
                checked:
                  lastCheckIn.job?.length > 0
                    ? lastCheckIn.job.some(
                        (job) =>
                          job.type === JobCheckInCheckOutEnum.CHECKIN &&
                          job.id === jobId.toString(),
                      )
                    : false,
              }
            : null,
          lastCheckOut: lastCheckOut
            ? {
                residentId: lastCheckOut.resident?._id,
                resident: lastCheckOut.resident?.fullInfomation,
                reservation: lastCheckOut._id,
                checked:
                  lastCheckOut.job?.length > 0
                    ? lastCheckOut.job.some(
                        (job) =>
                          job.type === JobCheckInCheckOutEnum.CHECKOUT &&
                          job.id === jobId.toString(),
                      )
                    : false,
              }
            : null,
        });
      }
    }

    return result.filter((v) => v.lastCheckIn || v.lastCheckOut);
  }

  async updateJobCheckInOrCheckOut(reservationPayload, jobId: string) {
    const foundReservation = await this.reservationModel
      .findById(reservationPayload.reservation)
      .lean();

    if (!foundReservation) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.RESERVATION_NOT_FOUND,
      );
    }

    let jobCheckInOrCheckOut = foundReservation.job;
    if (!jobCheckInOrCheckOut) {
      jobCheckInOrCheckOut = [];
      if (reservationPayload.checked) {
        jobCheckInOrCheckOut.push({
          id: jobId,
          type: reservationPayload.type,
        });
      }
    } else {
      if (
        reservationPayload.checked &&
        !jobCheckInOrCheckOut.some((job) => job.id === jobId)
      ) {
        jobCheckInOrCheckOut.push({
          id: jobId,
          type: reservationPayload.type,
        });
      } else if (
        !reservationPayload.checked &&
        jobCheckInOrCheckOut.some((job) => job.id === jobId)
      ) {
        jobCheckInOrCheckOut = jobCheckInOrCheckOut.filter(
          (job) => job.id !== jobId,
        );
      }
    }

    await this.reservationModel.updateOne(
      {
        _id: foundReservation._id,
      },
      {
        job: jobCheckInOrCheckOut,
      },
    );
  }

  async getReport(payload: GetNightRegistrationReportQueryDto) {
    const { location, dateFrom, dateTo, sortBy, sortDir } = payload;
    const locationInfos = await this.locationModel
      .aggregate([
        {
          $match: {
            _id: new ObjectId(location),
          },
        },
        {
          $lookup: {
            from: 'units',
            localField: '_id',
            foreignField: 'location',
            as: 'units',
            pipeline: [
              {
                $match: {
                  isActive: true,
                },
              },
              {
                $lookup: {
                  from: 'units',
                  localField: 'parent',
                  foreignField: '_id',
                  as: 'parent',
                },
              },
              {
                $set: {
                  parent: {
                    $arrayElemAt: ['$parent', 0],
                  },
                },
              },
              {
                $project: {
                  isRoot: 1,
                  name: 1,
                  maxOccupants: 1,
                  parent: 1,
                  position: 1,
                },
              },
            ],
          },
        },
        {
          $project: {
            units: 1,
            fullAddress: 1,
            costCenter: 1,
            maxOccupants: 1,
          },
        },
      ])
      .exec();
    const foundLocation = locationInfos[0];
    if (!foundLocation) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.LOCATION_NOT_FOUND,
      );
    }
    const filterDateFrom = dayjs(dateFrom).utc().startOf('day');
    const filterDateTo = dayjs(dateTo).utc().endOf('day');
    const unitOfLocations = foundLocation.units;
    const foundContracts = await this.contractModel
      .aggregate([
        {
          $addFields: {
            startDate: {
              $dateTrunc: { date: '$startDate', unit: 'day' },
            },
            endDate: {
              $dateTrunc: { date: '$endDate', unit: 'day' },
            },
          },
        },
        {
          $match: {
            type: {
              $in: [ContractType.RENTING, ContractType.SERVICE],
            },
            $or: [
              {
                location: foundLocation._id,
              },
              ...(foundLocation.costCenter
                ? [{ costCenter: foundLocation.costCenter }]
                : []),
            ],
            $and: [
              {
                startDate: {
                  $lte: filterDateTo.toDate(),
                },
              },
              {
                $or: [
                  {
                    endDate: {
                      $gte: filterDateFrom.toDate(),
                    },
                  },
                  {
                    endDate: null,
                  },
                ],
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'agreementlines',
            localField: 'agreementLines',
            foreignField: '_id',
            as: 'agreementLines',
            pipeline: [
              {
                $match: {
                  periodType: {
                    $ne: AgreementLinePeriodType.ONE_TIME,
                  },
                  $and: [
                    {
                      units: {
                        $exists: true,
                      },
                    },
                    {
                      units: {
                        $ne: [],
                      },
                    },
                  ],
                },
              },
              {
                $lookup: {
                  from: 'costlinegenerals',
                  localField: '_id',
                  foreignField: 'agreementLine',
                  as: 'costLineGenerals',
                  pipeline: [
                    {
                      $addFields: {
                        startDate: {
                          $dateTrunc: { date: '$startDate', unit: 'day' },
                        },
                        endDate: {
                          $dateTrunc: { date: '$endDate', unit: 'day' },
                        },
                      },
                    },
                    {
                      $match: {
                        startDate: {
                          $lte: filterDateTo.toDate(),
                        },
                        unit: {
                          $exists: true,
                        },
                        $or: [
                          {
                            endDate: {
                              $gte: filterDateFrom.toDate(),
                            },
                          },
                          {
                            endDate: null,
                          },
                        ],
                      },
                    },
                    {
                      $lookup: {
                        from: 'units',
                        localField: 'unit',
                        foreignField: '_id',
                        as: 'unit',
                        pipeline: [
                          {
                            $match: {
                              isActive: true,
                            },
                          },
                          {
                            $project: {
                              _id: 1,
                              parent: 1,
                              isRoot: 1,
                              name: 1,
                              maxOccupants: 1,
                            },
                          },
                        ],
                      },
                    },
                    {
                      $addFields: {
                        unit: {
                          $arrayElemAt: ['$unit', 0],
                        },
                      },
                    },
                    {
                      $project: {
                        _id: 1,
                        unit: 1,
                        startDate: 1,
                        endDate: 1,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'contacts',
            localField: 'contact',
            foreignField: '_id',
            as: 'contact',
            pipeline: [
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  displayName: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            contact: {
              $arrayElemAt: ['$contact', 0],
            },
          },
        },
        ...(sortBy && sortDir && sortBy === 'contact'
          ? [
              {
                $sort: {
                  'contact.displayName': (sortDir === 'asc' ? 1 : -1) as 1 | -1,
                },
              },
            ]
          : [
              {
                $sort: {
                  createdAt: 1 as 1 | -1,
                },
              },
            ]),
        {
          $project: {
            _id: 1,
            isWholeLocation: 1,
            type: 1,
            contact: 1,
            agreementLines: 1,
            startDate: 1,
            endDate: 1,
            createdAt: 1,
          },
        },
      ])
      .exec();

    const reservationDatas: any[] = [];

    const beds = await this.generateBedFromLocationAndContract(
      unitOfLocations,
      foundContracts,
    );

    const getReservationPromises = beds.map(async (bed) => {
      const reservationOfBeds = await this.reservationModel
        .aggregate([
          {
            $match: {
              unit: bed.unitId,
              bed: bed.bedNo,
              contact: {
                $exists: true,
              },
              isVirtual: false,
              $and: [
                {
                  arrivalDate: { $lte: filterDateTo.toDate() },
                  $or: [
                    {
                      departureDate: { $gte: filterDateFrom.toDate() },
                    },
                    {
                      departureDate: null,
                    },
                  ],
                },
              ],
            },
          },
          {
            $limit: 1,
          },
        ])
        .exec();
      if (reservationOfBeds.length > 0) {
        reservationDatas.push(
          ...reservationOfBeds.map((reservation) => {
            return { ...reservation, contact: bed.contact };
          }),
        );
      }
    });

    await Promise.all(getReservationPromises);

    const resultPromises = foundContracts.map(async (contract) => {
      const totalHiredDays = await this.calulateHiredDayOfContracts(
        foundLocation,
        contract,
        filterDateFrom,
        filterDateTo,
      );
      const totalOccupiedDays = this.sumOccupiedDaysOfContracts(
        contract,
        reservationDatas,
        filterDateFrom,
        filterDateTo,
      );
      const totalVacantDays = totalHiredDays - totalOccupiedDays;
      return {
        _id: contract._id,
        contact: contract.contact,
        totalHiredDays,
        totalVacantDays,
        totalOccupiedDays,
      };
    });
    let result = await Promise.all(resultPromises);
    if (sortBy && sortDir && sortBy !== 'contact') {
      switch (sortBy) {
        case 'totalHiredDays':
          result = _.orderBy(result, ['totalHiredDays'], [sortDir]);
          break;
        case 'totalOccupiedDays':
          result = _.orderBy(result, ['totalOccupiedDays'], [sortDir]);
          break;
        case 'totalVacantDays':
          result = _.orderBy(result, ['totalVacantDays'], [sortDir]);
          break;
        default:
          break;
      }
    }
    return result;
  }

  async exportReport(payload: GetNightRegistrationReportQueryDto) {
    const reportDatas = await this.getReport(payload);
    const fileName = `nr-report-${dayjs().utc().format(DATE_FORMAT_HYPHEN)}.csv`;
    const header = [
      { field: 'customer', title: 'Klant' },
      { field: 'totalOccupiedDays', title: 'Totaal bezette bedden' },
      { field: 'totalVacantDays', title: 'Totaal lege bedden' },
      { field: 'totalHiredDays', title: 'Totaal bedden' },
    ];

    const data = reportDatas.map((reportData) => {
      return {
        customer: reportData.contact.displayName,
        totalHiredDays: reportData.totalHiredDays,
        totalOccupiedDays: reportData.totalOccupiedDays,
        totalVacantDays: reportData.totalVacantDays,
      };
    });

    return { data, fileName, header };
  }

  async sendEmailResidentHasExceededMaximumLengthOfStay(
    payload: ResidentHasExceededMaximumLengthOfStayDto,
  ) {
    const { fakeCurrentDate, fakeLocationIds } = payload;

    const currentDate = fakeCurrentDate
      ? dayjs(fakeCurrentDate).utc().startOf('day').toDate()
      : dayjs().utc().startOf('day').toDate();

    const locations = await this.locationModel
      .aggregate([
        {
          $match: {
            isActive: true,
            isDeleted: false,
            $and: [
              {
                maximumStayDuration: {
                  $exists: true,
                },
              },
              {
                maximumStayDuration: {
                  $ne: null,
                },
              },
              {
                maximumStayDuration: {
                  $gt: 0,
                },
              },
            ],
            ...(fakeLocationIds && fakeLocationIds.length > 0
              ? {
                  _id: { $in: fakeLocationIds.map((id) => new ObjectId(id)) },
                }
              : {}),
          },
        },
        {
          $lookup: {
            from: 'units',
            localField: '_id',
            foreignField: 'location',
            as: 'units',
            let: { locMaxStayDuration: '$maximumStayDuration' },
            pipeline: [
              {
                $match: {
                  isActive: true,
                },
              },
              {
                $lookup: {
                  from: 'nightregistrationreservations',
                  localField: '_id',
                  foreignField: 'unit',
                  as: 'reservations',
                  let: { unit_locMaxStayDuration: '$$locMaxStayDuration' },
                  pipeline: [
                    {
                      $match: {
                        arrivalDate: { $lte: currentDate },
                        $or: [
                          { departureDate: { $gt: currentDate } },
                          { departureDate: null },
                        ],
                      },
                    },
                    {
                      $addFields: {
                        dateExceeded: {
                          $dateAdd: {
                            startDate: '$arrivalDate',
                            unit: 'month',
                            amount: '$$unit_locMaxStayDuration',
                          },
                        },
                      },
                    },
                    {
                      $addFields: {
                        hasExceededMaximumLengthOfStay: {
                          $cond: [
                            {
                              $eq: [
                                {
                                  $dateTrunc: {
                                    date: '$dateExceeded',
                                    unit: 'day',
                                  },
                                },
                                currentDate,
                              ],
                            },
                            true,
                            false,
                          ],
                        },
                      },
                    },
                    {
                      $match: {
                        hasExceededMaximumLengthOfStay: true,
                      },
                    },
                    {
                      $lookup: {
                        from: 'nightregistrationresidents',
                        localField: 'resident',
                        foreignField: '_id',
                        as: 'resident',
                        pipeline: [
                          {
                            $project: {
                              _id: 1,
                              firstName: 1,
                              lastName: 1,
                              displayName: 1,
                              dateOfBirth: 1,
                            },
                          },
                        ],
                      },
                    },
                    {
                      $set: {
                        resident: {
                          $arrayElemAt: ['$resident', 0],
                        },
                      },
                    },
                    {
                      $project: {
                        _id: 1,
                        resident: 1,
                        arrivalDate: 1,
                        departureDate: 1,
                        bed: 1,
                        hasExceededMaximumLengthOfStay: 1,
                        dateExceeded: 1,
                      },
                    },
                  ],
                },
              },
              {
                $match: {
                  reservations: { $ne: [] },
                },
              },
              {
                $lookup: {
                  from: 'units',
                  localField: 'parent',
                  foreignField: '_id',
                  as: 'parent',
                },
              },
              {
                $set: {
                  parent: {
                    $arrayElemAt: ['$parent', 0],
                  },
                },
              },
            ],
          },
        },
        {
          $match: {
            units: { $ne: [] },
          },
        },
      ])
      .exec();
    try {
      locations.forEach(async (location) => {
        const nrReceiverEmailExceededMaxiumLengthOfStay =
          this.configService.get<string>(
            'nightRegistration.nrReceiverEmailExceededMaxiumLengthOfStay',
          ) ?? '';

        const receiverEmail = !location.email
          ? nrReceiverEmailExceededMaxiumLengthOfStay
          : location.email;

        if (!receiverEmail) {
          this.logger.error(
            `Location ${location._id} does not have a receiver email for exceeded maximum length of stay notifications.`,
          );
          return;
        }

        const residents: any[] = [];
        location.units.forEach(async (unit) => {
          const hasParent = unit.parent && !unit.parent.isRoot;
          unit.reservations.forEach((reservation) => {
            if (reservation.resident) {
              residents.push({
                ...reservation.resident,
                unit: hasParent ? unit.parent.name : unit.name,
                bed: !hasParent
                  ? reservation.bed
                  : unit.name + '.' + reservation.bed,
              });
            }
          });
        });
        const residentDate = dayjs(currentDate)
          .subtract(location.maximumStayDuration, 'month')
          .format(DATE_FORMAT_SLASH);
        const emailData = {
          LOCATION_NAME: location.fullAddress,
          MAXIUM_LENGTH_OF_STAY: location.maximumStayDuration,
          DATE_EXCEEDED: dayjs(currentDate).format(DATE_FORMAT_SLASH),
          REGISTRATION_DATE: residentDate,
          RESIDENT_INFO:
            this.mapResidentInfoInEmailExceededMaxiumLengthOfStay(residents),
        };

        await this.getEmailTemplateAndSendEmail(
          'resident_has_exceeded_maxium_length_of_stay',
          receiverEmail,
          emailData,
        ).catch((error) => {
          this.logger.error(error);
        });
      });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
    return locations;
  }

  //#region Private methods
  async generateBedFromLocationAndContract(
    unitOfLocations: any[],
    foundContracts: any[],
  ) {
    const unitOfContracts: any[] = [];
    foundContracts.forEach((contract) => {
      if (contract.type === ContractType.RENTING) {
        const agreementLines = contract.agreementlines;
        const units = agreementLines.flatMap((agreementLine) => {
          const costLineGenerals = agreementLine.costLineGenerals;
          const unitOfCostLineGenerals = costLineGenerals.map(
            (costLineGeneral) => {
              return {
                ...costLineGeneral.unit,
                createdAt: contract.createdAt,
                contact: contract.contact ?? null,
              };
            },
          );
          return unitOfCostLineGenerals;
        });
        unitOfContracts.push(...units);
      } else {
        unitOfContracts.push(
          ...unitOfLocations.map((unit) => {
            return {
              ...unit,
              createdAt: contract.createdAt,
              contact: contract.contact ?? null,
            };
          }),
        );
      }
    });

    unitOfContracts.sort((a, b) => a.createdAt - b.createdAt);
    let contactForAllUnits = null;
    unitOfContracts.forEach((unit) => {
      if (unit.isRoot && unit.contact) {
        contactForAllUnits = unit.contact;
      }
    });

    let filteredUnits: any[] = [];

    //Get contact from contract for unit
    if (contactForAllUnits) {
      filteredUnits = unitOfLocations.map((unit) => ({
        ...unit,
        contact: contactForAllUnits,
      }));
    } else {
      filteredUnits = unitOfLocations.map((unitOfLocation) => {
        const unit = unitOfContracts
          .filter(
            (unit) =>
              unitOfLocation._id &&
              unit._id &&
              unit._id.toString() === unitOfLocation._id.toString(),
          )
          .sort((a, b) => a.createdAt - b.createdAt);
        const parentUnit = unitOfContracts
          .filter(
            (unit) =>
              unitOfLocation.parent &&
              unitOfLocation._id &&
              unit._id &&
              unit._id.toString() === unitOfLocation.parent._id.toString(),
          )
          .sort((a, b) => a.createdAt - b.createdAt);
        return {
          ...unitOfLocation,
          contact: parentUnit[0]?.contact ?? unit[0]?.contact ?? null,
        };
      });
    }

    //except root unit and unit has child and unit has max occupants = 0
    filteredUnits = filteredUnits.filter(
      (unit) =>
        unit.maxOccupants > 0 &&
        !unit.isRoot &&
        !unitOfLocations.some(
          (unitOfLocation) =>
            unitOfLocation.parent &&
            unitOfLocation.parent._id.toString() === unit._id.toString(),
        ),
    );

    //Sort unit by position
    const sortFilteredUnits = filteredUnits
      .map((filteredUnit) => {
        const parentUnit = filteredUnit.parent;
        const parentPosition = parentUnit.isRoot ? 0 : parentUnit.position || 0;
        const filteredPosition = filteredUnit.position || 0;
        const position = parentUnit.isRoot
          ? filteredPosition
          : Number(`${parentPosition}.${filteredPosition}`);
        return {
          ...filteredUnit,
          position,
        };
      })
      .sort((a, b) => a.position - b.position);

    //Generate beds
    const beds: any[] = [];
    sortFilteredUnits.forEach((sortFilteredUnit) => {
      const parentisRoot = sortFilteredUnit.parent.isRoot;

      for (let index = 1; index <= sortFilteredUnit.maxOccupants; index++) {
        const bedNoWithName = parentisRoot
          ? index
          : sortFilteredUnit.name + '.' + index;
        const unitName = parentisRoot
          ? sortFilteredUnit.name
          : sortFilteredUnit.parent.name || sortFilteredUnit.name;

        beds.push({
          nanoid: nanoid(),
          unitId: sortFilteredUnit._id,
          position: sortFilteredUnit.position,
          bedNo: index,
          bedNoWithName,
          unitName: unitName,
          contact: sortFilteredUnit.contact,
          reservation: {},
        });
      }
    });

    return beds;
  }

  private mapResidentInfoInEmailExceededMaxiumLengthOfStay(residents: any[]) {
    let residentInfos = '';
    residents.forEach(
      (resident) =>
        (residentInfos = residentInfos.concat(
          `<tr>
            <td>
              ${resident.displayName}
            </td>
            <td>
              ${resident.unit}
            </td>
            <td>
              ${resident.bed}
            </td>
          </tr>`,
        )),
    );
    return residentInfos;
  }

  private sumOccupiedDaysOfContracts(
    contract,
    reservations: any[],
    filterDateFrom,
    filterDateTo,
  ) {
    let occupiedDays = 0;
    const dayjsContractEndDate = dayjs(contract.endDate).utc().endOf('day');
    reservations.forEach((reservation) => {
      const { contact, arrivalDate, departureDate } = reservation;
      let fxEndDate = departureDate;
      if (
        contract.endDate &&
        !departureDate &&
        dayjsContractEndDate.isSameOrBefore(filterDateTo)
      ) {
        fxEndDate = dayjsContractEndDate.toDate();
      }

      if (
        contract.endDate &&
        !departureDate &&
        dayjsContractEndDate.isAfter(filterDateTo)
      ) {
        fxEndDate = filterDateTo.toDate();
      }

      if (!contract.endDate && !departureDate) {
        fxEndDate = filterDateTo.toDate();
      }

      if (contact?.displayName === contract.contact.displayName) {
        occupiedDays += this.calculateRangeDate(
          filterDateFrom,
          filterDateTo,
          arrivalDate,
          fxEndDate,
        );
      }
    });

    return occupiedDays;
  }

  private async calulateHiredDayOfContracts(
    location: any,
    contract: any,
    filterDateFrom,
    filterDateTo,
  ) {
    const costLineGenerals = contract.agreementLines.flatMap(
      (aggrementLine) => aggrementLine.costLineGenerals,
    );
    const hiredDayPromises = costLineGenerals.map((costLineGeneral) => {
      const { startDate, endDate, unit } = costLineGeneral;
      let maxOccupants = 0;
      if (contract.isWholeLocation || contract.type == ContractType.SERVICE) {
        maxOccupants = location.maxOccupants;
      } else {
        maxOccupants = unit.maxOccupants;
      }
      let fxEndDate = endDate;
      const contractEndDate = dayjs(contract.endDate).utc().endOf('day');
      if (
        contract.endDate &&
        !endDate &&
        contractEndDate.isSameOrBefore(filterDateTo)
      ) {
        fxEndDate = contractEndDate;
      } else if (
        contract.endDate &&
        !endDate &&
        contractEndDate.isAfter(filterDateTo)
      ) {
        fxEndDate = filterDateTo;
      } else if (
        contract.endDate &&
        endDate &&
        dayjs(endDate).utc().endOf('day').isSameOrBefore(filterDateTo)
      ) {
        fxEndDate = endDate;
      } else if (
        contract.endDate &&
        endDate &&
        dayjs(endDate).utc().endOf('day').isAfter(filterDateTo)
      ) {
        fxEndDate = filterDateTo;
      } else if (!contractEndDate.isSame(dayjs(endDate).utc().endOf('day'))) {
        fxEndDate = filterDateTo;
      }
      if (!contract.endDate) {
        fxEndDate = filterDateTo;
      }
      const daysHired = this.calculateRangeDate(
        filterDateFrom,
        filterDateTo,
        startDate,
        fxEndDate,
      );

      return daysHired * maxOccupants;
    });

    const hiredDays = await Promise.all(hiredDayPromises);
    return _.sumBy(hiredDays, (v) => v);
  }

  private calculateRangeDate(filterDateFrom, filterDateTo, startDate, endDate) {
    const dayjsStartDate = dayjs(startDate).utc().startOf('day');
    const dayjsEndDate = dayjs(endDate).utc().endOf('day');
    if (
      dayjsStartDate.isAfter(filterDateTo) ||
      dayjsEndDate.isBefore(filterDateFrom)
    ) {
      return 0;
    }

    let fxDateFrom = dayjsStartDate;
    let fxDateTo = dayjsEndDate;

    if (dayjsEndDate.isAfter(filterDateTo)) {
      fxDateTo = filterDateTo;
    }

    if (dayjsStartDate.isBefore(filterDateFrom)) {
      fxDateFrom = filterDateFrom;
    }

    return fxDateTo.diff(fxDateFrom, 'days') + 1;
  }

  private async validateImportData(
    convertedDatas: any[],
    foundCustomers: any[],
    foundNationalities: any[],
  ) {
    const errorsMissing = [];
    const errorsInvalid = [];
    for (let index = 0; index < convertedDatas.length; index++) {
      const data = convertedDatas[index];
      validateFirstName(
        index,
        data[HEADERS.FIRST_NAME],
        errorsMissing,
        errorsInvalid,
      );

      validateLastName(
        index,
        data[HEADERS.LAST_NAME],
        errorsMissing,
        errorsInvalid,
      );

      validateDateOfBirthFormat(
        index,
        data[HEADERS.DOB],
        errorsMissing,
        errorsInvalid,
      );

      validateGender(index, data[HEADERS.GENDER], errorsInvalid);

      await this.validateCustomerData(
        foundCustomers,
        index,
        data[HEADERS.CUSTOMER],
        errorsInvalid,
      );

      await this.validateNationalitiesData(
        foundNationalities,
        index,
        data[HEADERS.NATIONALITY],
        errorsInvalid,
      );

      validateClientId(index, data[HEADERS.CLIENT_ID], errorsInvalid);
      validatePhoneNumber(index, data[HEADERS.PHONE_NUM], errorsInvalid);
      validateEmail(index, data[HEADERS.EMAIL], errorsMissing, errorsInvalid);
    }
    return { errorsMissing, errorsInvalid };
  }

  private async validateCustomerData(
    foundCustomers: any[],
    index,
    customer,
    errorsInvalid,
  ) {
    if (!customer) {
      return;
    }
    const foundCustomer = foundCustomers.filter(
      (v) => v.displayName === customer,
    );

    if (!foundCustomer || foundCustomer.length === 0) {
      errorsInvalid.push({
        line: index + 2,
        field: HEADERS.CUSTOMER,
        message: 'Invalid customer',
      });
    }
  }

  private async validateNationalitiesData(
    foundNationalities: any[],
    index,
    code,
    errorsInvalid,
  ) {
    if (!code) {
      return;
    }
    const foundNationality = foundNationalities.filter((v) => v.code === code);

    if (!foundNationality || foundNationality.length === 0) {
      errorsInvalid.push({
        line: index + 2,
        field: HEADERS.NATIONALITY,
        message: 'Invalid nationality',
      });
    }
  }

  private async checkBedReserved(unit, bed, id) {
    const existedReservation = await this.reservationModel
      .find({
        $or: [
          {
            _id: { $ne: id },
            unit,
            bed,
            $or: [
              {
                departureDate: { $gt: dayjs().utc().toDate() },
              },
              {
                departureDate: null,
              },
            ],
          },
          {
            isVirtual: true,
            unit,
            bed,
          },
        ],
      })
      .lean();

    return existedReservation.length > 0;
  }

  async createOrUpdateResident(resident) {
    let foundResident: any = null;
    if (resident._id) {
      foundResident = await this.residentModel.findById(resident._id).lean();
      if (!foundResident) {
        throw new BadRequestException(
          NIGHT_REGISTRATION_MESSAGE_KEYS.RESIDENT_NOT_FOUND,
        );
      }

      const isExisted = await this.residentModel
        .findOne({
          _id: { $ne: foundResident._id },
          lastName: resident.lastName,
          $expr: {
            $eq: [
              { $dateToString: { format: '%d/%m/%Y', date: '$dateOfBirth' } },
              {
                $dateToString: {
                  format: '%d/%m/%Y',
                  date: dayjs(resident.dateOfBirth).utc().toDate(),
                },
              },
            ],
          },
        })
        .lean();
      if (isExisted) {
        throw new BadRequestException(
          NIGHT_REGISTRATION_MESSAGE_KEYS.RESIDENT_ALREADY_EXISTED,
        );
      }
    } else {
      foundResident = await this.residentModel
        .findOne({
          lastName: resident.lastName,
          $expr: {
            $eq: [
              { $dateToString: { format: '%d/%m/%Y', date: '$dateOfBirth' } },
              {
                $dateToString: {
                  format: '%d/%m/%Y',
                  date: dayjs(resident.dateOfBirth).utc().toDate(),
                },
              },
            ],
          },
        })
        .lean();
    }

    if (!foundResident) {
      foundResident = await this.residentModel.create({
        ..._.omit(resident, '_id'),
        dateOfBirth: dayjs(resident.dateOfBirth).utc().startOf('day').toDate(),
        displayName: `${resident.firstName} ${resident.lastName}`,
      });
    } else {
      foundResident = await this.residentModel.findByIdAndUpdate(
        foundResident._id,
        {
          ..._.omit(resident, '_id'),
          dateOfBirth: dayjs(resident.dateOfBirth)
            .utc()
            .startOf('day')
            .toDate(),
        },
        { new: true },
      );
    }

    return foundResident;
  }

  private async sendEmailToResident(
    resident,
    reservation,
    companyInfomation,
    unit,
    canSentArrivalEmail = true,
    canSentDepartureEmail = true,
  ) {
    const email = resident.email;
    if (!email) {
      return;
    }

    const locations = await this.locationModel
      .aggregate([
        {
          $match: {
            _id: unit.location,
          },
        },
        {
          $lookup: {
            from: 'addresses',
            localField: 'address',
            foreignField: '_id',
            as: 'address',
            pipeline: [
              {
                $lookup: {
                  from: 'regions',
                  localField: 'region',
                  foreignField: '_id',
                  as: 'region',
                },
              },
              {
                $set: {
                  region: {
                    $arrayElemAt: ['$region', 0],
                  },
                },
              },
            ],
          },
        },
        {
          $set: {
            address: {
              $arrayElemAt: ['$address', 0],
            },
          },
        },
        {
          $project: {
            fullAddress: 1,
            address: 1,
            email: 1,
          },
        },
      ])
      .exec();

    if (locations.length === 0) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.LOCATION_NOT_FOUND,
      );
    }

    const location = locations[0];

    const parentUnit = await this.unitModel
      .findOne({
        _id: unit.parent,
        isRoot: false,
      })
      .lean();

    const data = {
      LOCATION_NAME: location.fullAddress,
      UNIT: parentUnit ? parentUnit.name : unit.name,
      BED: parentUnit ? `${unit.name}.${reservation.bed}` : reservation.bed,
      STREET: location.address.street,
      STREET_NUMBER: location.address.streetNumber,
      SUFFIX: location.address.suffix,
      POSTAL_CODE: location.address.postalCode,
      CITY: location.address.city,
      REGION: location.address.region?.name,
      COMPANY_SIGNATURE: companyInfomation['sign'],
      COMPANY_BANNER: companyInfomation['banner'],
      COMPANY_TYPOGRAPHY_COLORS_PRIMARY:
        companyInfomation['typography']['colors']['primary'],
      COMPANY_TELEPHONE: companyInfomation['telephone'],
      COMPANY_ADDRESS1: companyInfomation['address1'],
      COMPANY_ADDRESS2: companyInfomation['address2'],
      COMPANY_WEBSITE: companyInfomation['website'],
      COMPANY_LOGO: companyInfomation['logo'],
      COMPANY_LINKEDIN_URL: companyInfomation['linkedinUrl'],
      COMPANY_LINKEDIN_LOGO: companyInfomation['linkedinLogo'],
      COMPANY_SFN_LOGO: companyInfomation['sfnLogo'],
      COMPANY_EMAIL:
        location.email && location.email !== ''
          ? location.email
          : companyInfomation['email'],
      COMPANY_NAME: companyInfomation['name'],
    };

    const currentDate = dayjs().utc().startOf('day');
    const arrivalDate = dayjs(reservation.arrivalDate).utc().startOf('day');
    const departureDate = reservation.departureDate
      ? dayjs(reservation.departureDate).utc().startOf('day')
      : null;

    if (currentDate.isSame(arrivalDate) && canSentArrivalEmail) {
      //TODO will do it later
      this.logger.log(`Send arrival email to resident ${resident._id}`);

      await this.getEmailTemplateAndSendEmail(
        'night_registration_arrival',
        email,
        data,
      );
    }

    if (departureDate && canSentDepartureEmail) {
      if (currentDate.isSame(departureDate)) {
        //TODO will do it later
        this.logger.log(`Send departure email to resident ${resident._id}`);
        await this.getEmailTemplateAndSendEmail(
          'night_registration_departure',
          email,
          data,
        );
      }

      if (currentDate.add(1, 'day').isSame(departureDate)) {
        //TODO will do it later
        this.logger.log(
          `Send before departure email to resident ${resident._id}`,
        );

        await this.getEmailTemplateAndSendEmail(
          'night_registration_before_departure',
          email,
          data,
        );
      }
    }
  }

  private async getEmailTemplateAndSendEmail(
    emailTemplateName,
    receiverEmail,
    data,
  ) {
    const emailTemplate = await this.emailTemplateModel
      .findOne({
        name: emailTemplateName,
      })
      .lean();

    if (!emailTemplate?.html) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.EMAIL_TEMPLATE_NOT_FOUND,
      );
    }

    const html = emailTemplate.html;
    const compiledObject = template(html);
    const htmlTemplate = compiledObject(data);
    await this.emailService.sendEmail({
      html: htmlTemplate,
      text: htmlTemplate,
      to: [receiverEmail],
      subject: emailTemplate.subject,
      bcc: emailTemplate.bcc,
      cc: emailTemplate.cc,
    });
  }

  private sortBedsWithReservations(bedWithReservations: any[], sort) {
    if (Object.keys(sort).length === 0) {
      return bedWithReservations;
    }

    const sortBy = Object.keys(sort)[0];
    const sortOrder = Object.values(sort)[0];

    switch (sortBy) {
      case 'unitName':
        return bedWithReservations.sort((a, b) => {
          return sortOrder === 1
            ? a.unitName.localeCompare(b.unitName)
            : b.unitName.localeCompare(a.unitName);
        });
      case 'arrivalDate':
        return bedWithReservations.sort((a, b) => {
          return sortOrder === 1
            ? a.reservation?.arrivalDate - b.reservation?.arrivalDate
            : b.reservation?.arrivalDate - a.reservation?.arrivalDate;
        });
      case 'departureDate':
        return bedWithReservations.sort((a, b) => {
          return sortOrder === 1
            ? a.reservation?.departureDate - b.reservation?.departureDate
            : b.reservation?.departureDate - a.reservation?.departureDate;
        });
      case 'contact':
        return bedWithReservations.sort((a, b) => {
          return sortOrder === 1
            ? a.reservation?.contact?.displayName.localeCompare(
                b.reservation?.contact?.displayName,
              )
            : b.reservation?.contact?.displayName.localeCompare(
                a.reservation?.contact?.displayName,
              );
        });
      default:
        // default sort by Unit Position
        return bedWithReservations.sort((a, b) => {
          return a.position - b.position;
        });
    }
  }

  private filterBedsWithReservations(bedWithReservations: any[], filter) {
    const filterArray = Array.isArray(filter) ? filter : [filter];
    let filteredBeds: any[] = [];
    const bedSet = new Set();
    filterArray.forEach((filterItem) => {
      let bedsToAdd: any[] = [];
      switch (filterItem) {
        case 'availableBeds':
          bedsToAdd = bedWithReservations.filter(
            (bed) => !bed.reservation?._id && !bed.reservation?.isVirtual,
          );
          break;
        case 'reservedBeds':
          bedsToAdd = bedWithReservations.filter(
            (bed) =>
              bed.reservation?._id && bed.reservation?.isVirtual === true,
          );
          break;
        case 'occupiedBeds':
          bedsToAdd = bedWithReservations.filter(
            (bed) => bed.reservation?.resident?._id,
          );
          break;
        case 'onlyForMaleBeds':
          bedsToAdd = bedWithReservations.filter(
            (bed) => bed.availableGender === 'Male',
          );
          break;
        case 'onlyForFemaleBeds':
          bedsToAdd = bedWithReservations.filter(
            (bed) => bed.availableGender === 'Female',
          );
          break;
        default:
          break;
      }
      bedsToAdd.forEach((bed) => bedSet.add(bed));
    });
    filteredBeds = Array.from(bedSet);
    return filteredBeds;
  }

  async mapReservationToBeds(
    beds: any[],
    checkOut,
    maximumStayDuration?: number | null,
  ) {
    const defaultReservation = {
      arrivalDate: null,
      departureDate: null,
      daysLeft: null,
      maximumStayWarning: null,
      bed: null,
      contact: null,
      remark: null,
      resident: {
        firstName: null,
        lastName: null,
        gender: null,
      },
    };

    const mapPromise = beds.map(async (bed) => {
      const current = dayjs().utc();
      let condition: any = {
        $or: [
          { departureDate: { $gt: current.endOf('day').toDate() } },
          { departureDate: { $eq: null } },
        ],
      };

      if (checkOut === 'true') {
        condition = {
          $and: [
            {
              departureDate: {
                $gte: current.add(-2, 'day').startOf('day').toDate(),
              },
            },
            { departureDate: { $lte: current.toDate() } },
          ],
        };
      }

      const reservation = await this.reservationModel
        .aggregate()
        .match({
          unit: bed.unitId,
          bed: bed.bedNo,
          ...condition,
        })
        .lookup({
          from: 'nightregistrationresidents',
          localField: 'resident',
          foreignField: '_id',
          as: 'resident',
          pipeline: [
            {
              $lookup: {
                from: 'contacts',
                localField: 'contact',
                foreignField: '_id',
                as: 'contact',
                pipeline: [
                  {
                    $project: {
                      displayName: 1,
                    },
                  },
                ],
              },
            },
            {
              $set: {
                contact: {
                  $arrayElemAt: ['$contact', 0],
                },
              },
            },
            {
              $lookup: {
                from: 'nightregistrationnationalities',
                localField: 'nationality',
                foreignField: '_id',
                as: 'nationality',
                pipeline: [
                  {
                    $project: {
                      _id: 1,
                      name: 1,
                      nameDutch: 1,
                      code: 1,
                    },
                  },
                ],
              },
            },
            {
              $set: {
                nationality: {
                  $arrayElemAt: ['$nationality', 0],
                },
              },
            },
            {
              $project: {
                firstName: 1,
                lastName: 1,
                gender: 1,
                dateOfBirth: 1,
                clientId: 1,
                phoneNumber: 1,
                email: 1,
                nationality: 1,
                fullInfomation: {
                  $concat: [
                    '$lastName',
                    ', ',
                    '$firstName',
                    ' - ',
                    {
                      $dateToString: {
                        format: '%d/%m/%Y',
                        date: '$dateOfBirth',
                      },
                    },
                  ],
                },
                contact: 1,
              },
            },
          ],
        })
        .addFields({ resident: { $arrayElemAt: ['$resident', 0] } })
        .lookup({
          from: 'nightregistrationwarnings',
          localField: 'resident._id',
          foreignField: 'resident',
          as: 'warnings',
          pipeline: [
            {
              $sort: {
                level: -1,
                emailTemplateOption: -1,
                createdAt: -1,
              },
            },
            {
              $limit: 1,
            },
          ],
        })
        .lookup({
          from: 'contacts',
          localField: 'contact',
          foreignField: '_id',
          as: 'contact',
          pipeline: [
            {
              $project: {
                displayName: 1,
                identifier: 1,
              },
            },
          ],
        })
        .addFields({
          contact: {
            $arrayElemAt: ['$contact', 0],
          },
          daysLeft: {
            $cond: {
              if: {
                $or: [
                  { $eq: [maximumStayDuration ?? null, null] },
                  { $eq: ['$arrivalDate', null] },
                  { $gt: ['$arrivalDate', current.toDate()] },
                ],
              },
              then: null,
              else: {
                $dateDiff: {
                  startDate: current.toDate(),
                  endDate: {
                    $dateAdd: {
                      startDate: '$arrivalDate',
                      unit: 'month',
                      amount: maximumStayDuration,
                    },
                  },
                  unit: 'day',
                },
              },
            },
          },
        })
        .addFields({
          maximumStayWarning: {
            $cond: {
              if: { $eq: ['$daysLeft', null] },
              then: null,
              else: {
                $switch: {
                  branches: [
                    {
                      case: { $lte: ['$daysLeft', 0] },
                      then: NRMaximumStayDurationLevel.EXCEEDED,
                    },
                    {
                      case: { $lte: ['$daysLeft', 14] },
                      then: NRMaximumStayDurationLevel.APPROACHING,
                    },
                  ],
                  default: null,
                },
              },
            },
          },
        })
        .sort({
          isVirtual: -1,
          arrivalDate: -1,
          createdAt: -1,
        })
        .limit(1)
        .option({ allowDiskUse: true })
        .exec();

      if (reservation.length === 0) {
        reservation.push({
          ...defaultReservation,
          contact: bed.contact ?? null,
        });
      }

      if (
        reservation[0]._id &&
        reservation[0].resident &&
        reservation[0].warnings &&
        reservation[0].warnings.length > 0
      ) {
        reservation[0] = {
          ..._.omit(reservation[0], 'resident'),
          resident: {
            ...reservation[0].resident,
            level: reservation[0].warnings[0].level,
            dayToLeave: reservation[0].warnings[0].dayToLeave,
          },
          emailTemplateOption: reservation[0].warnings[0].emailTemplateOption,
        };
      }

      if (
        reservation[0]._id &&
        reservation[0].resident &&
        ((reservation[0].warnings && reservation[0].warnings.length === 0) ||
          !reservation[0].warnings)
      ) {
        reservation[0] = {
          ...reservation[0],
          level: NightRegistrationWarningLevel.GOOD,
          emailTemplateOption: 1,
        };
      }

      //Remove list warnings
      delete reservation[0].warnings;

      return {
        ...bed,
        reservation: reservation[0],
      };
    });
    return await Promise.all(mapPromise);
  }

  private async sendEmailWarningToContact(
    reservation,
    warning,
    warningCategory,
    companyInfomation,
    publicPdfUrl,
  ) {
    const contact = reservation.contact;

    const resident = await this.residentModel.findById(reservation.resident);
    const receiverEmail = resident?.email;
    if (!receiverEmail) {
      return;
    }

    const level = warning.level.split('|')[1];

    const foundUnit = await this.unitModel.findById(reservation.unit).lean();
    if (!foundUnit) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.UNIT_NOT_FOUND,
      );
    }

    const locations = await this.locationModel
      .aggregate([
        {
          $match: {
            _id: foundUnit.location,
          },
        },
        {
          $project: {
            fullAddress: 1,
            email: 1,
          },
        },
      ])
      .exec();

    if (locations.length === 0) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.LOCATION_NOT_FOUND,
      );
    }

    const location = locations[0];

    const data = {
      FIRSTNAME: resident?.firstName,
      LASTNAME: resident?.lastName,
      DATE_WARNING: dayjs(warning.warningDate).utc().format(DATE_FORMAT_SLASH),
      TIME_WARNING: warning.warningTime ?? '00:00',
      LOCATION_NAME: location.fullAddress,
      WARNING_CATEGORY: warningCategory?.category,
      WARNING_DESCRIPTION: warning.description,
      COMPANY_SIGNATURE: companyInfomation['sign'],
      COMPANY_BANNER: companyInfomation['banner'],
      COMPANY_TYPOGRAPHY_COLORS_PRIMARY:
        companyInfomation['typography']['colors']['primary'],
      COMPANY_TYPOGRAPHY_COLORS_SIGN_TEXT:
        companyInfomation['typography']['colors']['signText'] ?? '#D2232A',
      COMPANY_TELEPHONE: companyInfomation['telephone'],
      COMPANY_ADDRESS1: companyInfomation['address1'],
      COMPANY_ADDRESS2: companyInfomation['address2'],
      COMPANY_WEBSITE: companyInfomation['website'],
      COMPANY_LOGO: companyInfomation['logo'],
      COMPANY_NR_LOGO: companyInfomation['nrLogo'],
      COMPANY_LINKEDIN_URL: companyInfomation['linkedinUrl'],
      COMPANY_LINKEDIN_LOGO: companyInfomation['linkedinLogo'],
      COMPANY_SFN_LOGO: companyInfomation['sfnLogo'],
      COMPANY_EMAIL:
        location.email && location.email !== ''
          ? location.email
          : companyInfomation['email'],
      COMPANY_NAME: companyInfomation['name'],
      DISPLAY_BANNER:
        companyInfomation['displayBanner'] === true ? 'block' : 'none',
      DISPLAY_LINKEDIN:
        companyInfomation['displayLinkedIn'] === true ? 'block' : 'none',
    };

    if (level === 'removal') {
      data['DAY_TO_LEAVE'] = formatDate(warning.dayToLeave);
    }

    const emailTemplateName = getEmailTemplateName(
      warning.emailTemplateOption,
      level,
    );
    const emailTemplate = await this.emailTemplateModel
      .findOne({
        name: emailTemplateName,
      })
      .lean();

    if (!emailTemplate?.html) {
      throw new BadRequestException(
        NIGHT_REGISTRATION_MESSAGE_KEYS.EMAIL_TEMPLATE_NOT_FOUND,
      );
    }

    const nrCcEmails =
      this.configService.get<string>('nightRegistration.nrCcEmails') || '';
    const listSplitEmails = !nrCcEmails ? [] : nrCcEmails.split(',');

    const html = emailTemplate.html;
    const compiledObject = template(html);
    const htmlTemplate = compiledObject(data);
    const subject = getEmailSubject(
      warning.emailTemplateOption,
      level,
      warning.warningDate,
      resident?.clientId,
      resident?.lastName,
      location.fullAddress,
    );

    const attachmentName = getNightRegistrationPDFFileNameByLevel(
      warning.level,
      warning.emailTemplateOption,
      warning.warningDate,
      resident?.lastName,
      location.fullAddress,
    );

    await this.emailService.sendEmail({
      html: htmlTemplate,
      text: htmlTemplate,
      to: [receiverEmail],
      subject: subject ?? '',
      bcc: [],
      cc: contact.warningEmail
        ? [contact.warningEmail, ...listSplitEmails]
        : [...listSplitEmails],
      attachments: publicPdfUrl
        ? [
            {
              filename: attachmentName,
              path: publicPdfUrl,
            },
          ]
        : [],
    });
  }

  private readonly getNationalityNoCode = (isReturnId: boolean = false) => {
    const noCodeNationality = this.nationalityModel.findOne({
      code: NATIONALITY_NO_CODE,
    });
    return isReturnId ? noCodeNationality._id : noCodeNationality;
  };
  //#endregion
}
