import { Test } from '@nestjs/testing';

import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import {
  initMockRoleGroup,
  mockRoleGroupData,
} from '~/test/mocks/rolegroup.mock';

import { RoleGroupModel } from '../role-group.model';
import { RoleGroupService } from '../role-group.service';
import { roleGroupTest } from './role-group.dto.test';

describe('RoleGroupService', () => {
  let service: RoleGroupService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [RoleGroupService, ...testInjectModel([RoleGroupModel])],
    }).compile();

    service = module.get(RoleGroupService);

    // Init data
    await initMockRoleGroup();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('create', () => {
    it('should create success role group', async () => {
      const data: Partial<RoleGroupModel> = {
        name: 'Test Role Group',
        description: 'Test Description',
        isActive: true,
        roles: [],
      };
      const result = await service.create(data);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(roleGroupTest.modelSchema);
    });
  });

  describe('findAll', () => {
    it('should return all role groups', async () => {
      const result = await service.findAll();
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThanOrEqual(2);
    });
  });

  describe('findOne', () => {
    it('should return a role group by id', async () => {
      const id = mockRoleGroupData._id.toString();
      const result = await service.findOne(id);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockRoleGroupData._id);
    });
  });

  describe('update', () => {
    it('should update a role group', async () => {
      const id = mockRoleGroupData._id.toString();
      const data: Partial<RoleGroupModel> = {
        name: 'Updated Role Group',
        description: 'Updated Description',
        isActive: false,
        roles: [],
      };
      const result = await service.update(id, data);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockRoleGroupData._id);
    });
  });

  describe('delete', () => {
    it('should delete a role group', async () => {
      const id = mockRoleGroupData._id.toString();
      const result = await service.delete(id);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockRoleGroupData._id);
    });
  });
});
