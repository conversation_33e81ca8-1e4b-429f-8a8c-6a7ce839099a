# Default values for node projects.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1
image:
  repository: infodation.azurecr.io/ee-acc/ee-acc-v2-core
  tag: 0.0.7
  pullPolicy: IfNotPresent

# define environment variables here as a map of key: value
env:

# enable this flag to use knative serve to deploy the ap


nodeSelector: {}

service:
  name: ee-acc-v2-core
  type: ClusterIP
  externalPort: 2345
  internalPort: 2345
  annotations:
    fabric8.io/expose: "true"
    fabric8.io/ingress.annotations: "kubernetes.io/ingress.class: nginx\nnginx.ingress.kubernetes.io/proxy-body-size: 50m\nnginx.ingress.kubernetes.io/proxy-buffer-size: 128k\nnginx.ingress.kubernetes.io/proxy-buffers-number: 4 256k"    
resources:
  limits:
    cpu: 1024m
    memory: 1024Mi
  requests:
    cpu: 1024m
    memory: 1024Mi
probePath: /app
livenessProbe:
  initialDelaySeconds: 120
  periodSeconds: 20
  successThreshold: 1
  timeoutSeconds: 10
readinessProbe:
  initialDelaySeconds: 120
  failureThreshold: 1
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 10

affinity: {}