import { Injectable } from '@nestjs/common';
import { ReturnModelType } from '@typegoose/typegoose';

import { InjectModel } from '~/transformers/model.transformer';

import { RoleGroupModel } from './role-group.model';

@Injectable()
export class RoleGroupService {
  constructor(
    @InjectModel(RoleGroupModel)
    private readonly roleGroupModel: ReturnModelType<typeof RoleGroupModel>,
  ) {}

  async create(data: Partial<RoleGroupModel>) {
    return this.roleGroupModel.create(data);
  }

  async findAll() {
    return this.roleGroupModel.find().exec();
  }

  async findOne(id: string) {
    return this.roleGroupModel.findById(id).exec();
  }

  async update(id: string, data: Partial<RoleGroupModel>) {
    return this.roleGroupModel
      .findByIdAndUpdate(id, data, { new: true })
      .exec();
  }

  async delete(id: string) {
    return this.roleGroupModel.findByIdAndDelete(id).exec();
  }
}
