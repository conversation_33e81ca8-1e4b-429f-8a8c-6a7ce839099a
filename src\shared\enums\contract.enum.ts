export enum ContractType {
  JOB = 'job',
  RENTING = 'debtor-renting',
  CREDITOR = 'creditor-renting',
  CUSTOM = 'custom',
  SERVICE = 'debtor-service',
  SUPPLIER = 'supplier',
}

export enum AgreementLineType {
  SERVICE = 'service',
  PRODUCT = 'product',
  ACCOMMODATION = 'accommodation',
}

export enum AgreementLinePeriod {
  WEEKLY = 'weekly',
  FOUR_WEEKLY = 'four-weekly',
  MONTHLY = 'monthly',
}

export enum AgreementLinePeriodType {
  PERIODIC = 'periodic',
  ONE_TIME = 'one-time',
  DEPOSIT = 'deposit',
}

export enum CostLineStatus {
  OPEN = 'open',
  CLOSED = 'closed',
  CANCELED = 'canceled',
}

export enum InvoiceType {
  JOB = 'job',
  RENTING = 'debtor-renting',
  CUSTOM = 'custom',
  SERVICE = 'debtor-service',
}

export enum NoticeDays {
  ONE_WEEK = '1 week',
  TWO_WEEKS = '2 weeks',
  ONE_MONTH = '1 month',
  ONE_CALENDARMONTH = '1 calendarmonth',
  TWO_MONTHS = '2 months',
  THREE_MONTHS = '3 months',
  SIX_MONTHS = '6 months',
  TWELVE_MONTHS = '12 months',
}

export enum OwnerType {
  HOMEE = 'HomEE',
  LENTO = 'Lento',
}
