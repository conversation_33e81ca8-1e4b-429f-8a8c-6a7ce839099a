import dayjs from 'dayjs';

import { DATE_FORMAT_ABBREVIATION } from '~/constants/app.constant';
import {
  NightRegistrationGender,
  NightRegistrationWarningLevel,
  NRMaximumStayDurationLevel,
  NRMaximumStayDurationLevelLabel,
} from '~/shared/enums/night-registration.enum';

import {
  checkCorrectHeaders,
  convertStayWarningLabel,
  getEmailSubject,
  getEmailTemplateName,
  getFullEmailTemplateOption,
  getGender,
  getLevelForExport,
  getNightRegistrationPDFFileNameByLevel,
  HEADERS,
  validateClientId,
  validateDateOfBirthFormat,
  validateEmail,
  validateFirstName,
  validateGender,
  validateLastName,
  validatePhoneNumber,
} from '../night-registration.util';

describe('Night Registration Util', () => {
  describe('validateEmail', () => {
    it('should add error when email is missing', () => {
      const errorsMissing = [];
      const errorsInvalid = [];

      validateEmail(0, '', errorsMissing, errorsInvalid);

      expect(errorsMissing).toHaveLength(1);
      expect(errorsMissing[0]).toEqual({
        line: 2,
        field: HEADERS.EMAIL,
        message: 'Missing email',
      });
    });

    it('should add error when email is invalid', () => {
      const errorsMissing = [];
      const errorsInvalid = [];

      validateEmail(0, 'invalid-email', errorsMissing, errorsInvalid);

      expect(errorsInvalid).toHaveLength(1);
      expect(errorsInvalid[0]).toEqual({
        line: 2,
        field: HEADERS.EMAIL,
        message: 'Invalid email',
      });
    });

    it('should not add error when email is valid', () => {
      const errorsMissing = [];
      const errorsInvalid = [];

      validateEmail(0, '<EMAIL>', errorsMissing, errorsInvalid);

      expect(errorsMissing).toHaveLength(0);
      expect(errorsInvalid).toHaveLength(0);
    });
  });

  describe('validatePhoneNumber', () => {
    it('should add error when phone number is invalid', () => {
      const errorsInvalid = [];

      validatePhoneNumber(0, 'invalid-phone', errorsInvalid);

      expect(errorsInvalid).toHaveLength(1);
      expect(errorsInvalid[0]).toEqual({
        line: 2,
        field: HEADERS.PHONE_NUM,
        message: 'Invalid phone number that must be 10-24 digits',
      });
    });

    it('should not add error when phone number is valid', () => {
      const errorsInvalid = [];

      validatePhoneNumber(0, '+1234567890', errorsInvalid);

      expect(errorsInvalid).toHaveLength(0);
    });

    it('should not add error when phone number is empty', () => {
      const errorsInvalid = [];

      validatePhoneNumber(0, '', errorsInvalid);

      expect(errorsInvalid).toHaveLength(0);
    });
  });

  describe('validateClientId', () => {
    it('should add error when client ID is invalid', () => {
      const errorsInvalid = [];

      validateClientId(0, 'invalid@client', errorsInvalid);

      expect(errorsInvalid).toHaveLength(1);
      expect(errorsInvalid[0]).toEqual({
        line: 2,
        field: HEADERS.CLIENT_ID,
        message: 'Invalid client ID that must be 1-15 characters',
      });
    });

    it('should not add error when client ID is valid', () => {
      const errorsInvalid = [];

      validateClientId(0, 'ABC123', errorsInvalid);

      expect(errorsInvalid).toHaveLength(0);
    });

    it('should not add error when client ID is empty', () => {
      const errorsInvalid = [];

      validateClientId(0, '', errorsInvalid);

      expect(errorsInvalid).toHaveLength(0);
    });
  });

  describe('validateGender', () => {
    it('should add error when gender is invalid', () => {
      const errorsInvalid = [];

      validateGender(0, 'INVALID', errorsInvalid);

      expect(errorsInvalid).toHaveLength(1);
      expect(errorsInvalid[0]).toEqual({
        line: 2,
        field: HEADERS.GENDER,
        message: 'Invalid gender that must be [MALE, M, FEMALE, F]',
      });
    });

    it('should not add error when gender is valid', () => {
      const errorsInvalid = [];

      validateGender(0, 'MALE', errorsInvalid);
      validateGender(1, 'M', errorsInvalid);
      validateGender(2, 'FEMALE', errorsInvalid);
      validateGender(3, 'F', errorsInvalid);

      expect(errorsInvalid).toHaveLength(0);
    });

    it('should not add error when gender is empty', () => {
      const errorsInvalid = [];

      validateGender(0, '', errorsInvalid);

      expect(errorsInvalid).toHaveLength(0);
    });
  });

  describe('validateDateOfBirthFormat', () => {
    it('should add error when date is missing', () => {
      const errorsMissing = [];
      const errorsInvalid = [];

      validateDateOfBirthFormat(0, '', errorsMissing, errorsInvalid);

      expect(errorsMissing).toHaveLength(1);
      expect(errorsMissing[0]).toEqual({
        line: 2,
        field: HEADERS.DOB,
        message: 'Missing date of birth',
      });
    });

    it('should add error when date format is invalid', () => {
      const errorsMissing = [];
      const errorsInvalid = [];

      validateDateOfBirthFormat(0, '2023/12/01', errorsMissing, errorsInvalid);

      expect(errorsInvalid).toHaveLength(1);
      expect(errorsInvalid[0]).toEqual({
        line: 2,
        field: HEADERS.DOB,
        message: 'Invalid date of birth that must be dd/mm/yyyy or dd-mm-yyyy',
      });
    });

    it('should not add error when date format is valid', () => {
      const errorsMissing = [];
      const errorsInvalid = [];

      validateDateOfBirthFormat(0, '01/12/2023', errorsMissing, errorsInvalid);
      validateDateOfBirthFormat(1, '01-12-2023', errorsMissing, errorsInvalid);

      expect(errorsMissing).toHaveLength(0);
      expect(errorsInvalid).toHaveLength(0);
    });
  });

  describe('validateFirstName', () => {
    it('should add error when first name is missing', () => {
      const errorsMissing = [];
      const errorsInvalid = [];

      validateFirstName(0, '', errorsMissing, errorsInvalid);

      expect(errorsMissing).toHaveLength(1);
      expect(errorsMissing[0]).toEqual({
        line: 2,
        field: HEADERS.FIRST_NAME,
        message: 'Missing first name',
      });
    });

    it('should add error when first name is invalid', () => {
      const errorsMissing = [];
      const errorsInvalid = [];

      validateFirstName(0, 'John123', errorsMissing, errorsInvalid);

      expect(errorsInvalid).toHaveLength(1);
      expect(errorsInvalid[0]).toEqual({
        line: 2,
        field: HEADERS.FIRST_NAME,
        message: 'Invalid first name',
      });
    });

    it('should not add error when first name is valid', () => {
      const errorsMissing = [];
      const errorsInvalid = [];

      validateFirstName(0, 'John Doe', errorsMissing, errorsInvalid);

      expect(errorsMissing).toHaveLength(0);
      expect(errorsInvalid).toHaveLength(0);
    });
  });

  describe('validateLastName', () => {
    it('should add error when last name is missing', () => {
      const errorsMissing = [];
      const errorsInvalid = [];

      validateLastName(0, '', errorsMissing, errorsInvalid);

      expect(errorsMissing).toHaveLength(1);
      expect(errorsMissing[0]).toEqual({
        line: 2,
        field: HEADERS.LAST_NAME,
        message: 'Missing last name',
      });
    });

    it('should add error when last name is invalid', () => {
      const errorsMissing = [];
      const errorsInvalid = [];

      validateLastName(0, 'Smith123', errorsMissing, errorsInvalid);

      expect(errorsInvalid).toHaveLength(1);
      expect(errorsInvalid[0]).toEqual({
        line: 2,
        field: HEADERS.LAST_NAME,
        message: 'Invalid last name',
      });
    });

    it('should not add error when last name is valid', () => {
      const errorsMissing = [];
      const errorsInvalid = [];

      validateLastName(0, 'Smith', errorsMissing, errorsInvalid);

      expect(errorsMissing).toHaveLength(0);
      expect(errorsInvalid).toHaveLength(0);
    });
  });

  describe('checkCorrectHeaders', () => {
    it('should return true when all headers are correct', () => {
      const fileHeaders = Object.values(HEADERS);

      const result = checkCorrectHeaders(fileHeaders);

      expect(result).toBe(true);
    });

    it('should return false when headers are incorrect', () => {
      const fileHeaders = ['Invalid Header', 'Another Invalid'];

      const result = checkCorrectHeaders(fileHeaders);

      expect(result).toBe(false);
    });
  });

  describe('convertStayWarningLabel', () => {
    it('should return correct label for EXCEEDED', () => {
      const result = convertStayWarningLabel(
        NRMaximumStayDurationLevel.EXCEEDED,
      );

      expect(result).toBe(NRMaximumStayDurationLevelLabel.EXCEEDED);
    });

    it('should return correct label for APPROACHING', () => {
      const result = convertStayWarningLabel(
        NRMaximumStayDurationLevel.APPROACHING,
      );

      expect(result).toBe(NRMaximumStayDurationLevelLabel.APPROACHING);
    });

    it('should return empty string for unknown level', () => {
      const result = convertStayWarningLabel('UNKNOWN');

      expect(result).toBe('');
    });
  });

  describe('getLevelForExport', () => {
    it('should return correct level for GOOD', () => {
      const result = getLevelForExport(NightRegistrationWarningLevel.GOOD, 1);

      expect(result).toBe('Good');
    });

    it('should return correct level for VERBAL with 1st option', () => {
      const result = getLevelForExport(NightRegistrationWarningLevel.VERBAL, 1);

      expect(result).toBe('1st notification');
    });

    it('should return correct level for OFFICIAL with 2nd option', () => {
      const result = getLevelForExport(
        NightRegistrationWarningLevel.OFFICIAL,
        2,
      );

      expect(result).toBe('2nd official warning');
    });

    it('should return correct level for REMOVAL', () => {
      const result = getLevelForExport(
        NightRegistrationWarningLevel.REMOVAL,
        1,
      );

      expect(result).toBe('Removal');
    });

    it('should return empty string for unknown level', () => {
      const result = getLevelForExport('UNKNOWN', 1);

      expect(result).toBe('');
    });
  });

  describe('getGender', () => {
    it('should return MALE for MALE input', () => {
      const result = getGender('MALE');

      expect(result).toBe(NightRegistrationGender.MALE);
    });

    it('should return MALE for M input', () => {
      const result = getGender('M');

      expect(result).toBe(NightRegistrationGender.MALE);
    });

    it('should return FEMALE for FEMALE input', () => {
      const result = getGender('FEMALE');

      expect(result).toBe(NightRegistrationGender.FEMALE);
    });

    it('should return FEMALE for F input', () => {
      const result = getGender('F');

      expect(result).toBe(NightRegistrationGender.FEMALE);
    });

    it('should return MALE for empty input', () => {
      const result = getGender('');

      expect(result).toBe(NightRegistrationGender.MALE);
    });

    it('should return MALE for null input', () => {
      const result = getGender(null);

      expect(result).toBe(NightRegistrationGender.MALE);
    });
  });

  describe('getEmailTemplateName', () => {
    it('should return correct template name for verbal with option 1', () => {
      const result = getEmailTemplateName(1, 'verbal');

      expect(result).toBe('night_registration_1st_verbal');
    });

    it('should return correct template name for official with option 2', () => {
      const result = getEmailTemplateName(2, 'official');

      expect(result).toBe('night_registration_2nd_official');
    });

    it('should return correct template name for removal', () => {
      const result = getEmailTemplateName(1, 'removal');

      expect(result).toBe('night_registration_removal');
    });
  });

  describe('getNightRegistrationPDFFileNameByLevel', () => {
    const testDate = '2023-12-01';
    const lastName = 'Smith';
    const address = '123 Main St';

    it('should return correct filename for first verbal notification', () => {
      const result = getNightRegistrationPDFFileNameByLevel(
        'warning|verbal',
        1,
        testDate,
        lastName,
        address,
      );

      expect(result).toBe(
        `${dayjs(testDate).format(DATE_FORMAT_ABBREVIATION)}_First Verbal Notification_${lastName}_${address}.pdf`,
      );
    });

    it('should return correct filename for second official warning', () => {
      const result = getNightRegistrationPDFFileNameByLevel(
        'warning|official',
        2,
        testDate,
        lastName,
        address,
      );

      expect(result).toBe(
        `${dayjs(testDate).format(DATE_FORMAT_ABBREVIATION)}_Second Official Warning_${lastName}_${address}.pdf`,
      );
    });

    it('should return correct filename for removal', () => {
      const result = getNightRegistrationPDFFileNameByLevel(
        'warning|removal',
        1,
        testDate,
        lastName,
        address,
      );

      expect(result).toBe(
        `${dayjs(testDate).format(DATE_FORMAT_ABBREVIATION)}_Removal_${lastName}_${address}.pdf`,
      );
    });
  });

  describe('getFullEmailTemplateOption', () => {
    it('should return "First" for option 1', () => {
      const result = getFullEmailTemplateOption(1);

      expect(result).toBe('First');
    });

    it('should return "Second" for option 2', () => {
      const result = getFullEmailTemplateOption(2);

      expect(result).toBe('Second');
    });

    it('should return empty string for unknown option', () => {
      const result = getFullEmailTemplateOption(3);

      expect(result).toBe('');
    });
  });

  describe('getEmailSubject', () => {
    const testDate = '2023-12-01';
    const clientId = 'C123';
    const lastName = 'Smith';
    const address = '123 Main St';

    it('should return correct subject for verbal notification', () => {
      const result = getEmailSubject(
        1,
        'verbal',
        testDate,
        clientId,
        lastName,
        address,
      );

      expect(result).toBe(
        `First Verbal Notification ${dayjs(testDate).format(DATE_FORMAT_ABBREVIATION)}_${clientId}, ${lastName}, ${address}`,
      );
    });

    it('should return correct subject for official warning', () => {
      const result = getEmailSubject(
        2,
        'official',
        testDate,
        clientId,
        lastName,
        address,
      );

      expect(result).toBe(
        `Second Official Warning ${dayjs(testDate).format(DATE_FORMAT_ABBREVIATION)}_${clientId}, ${lastName}, ${address}`,
      );
    });

    it('should return correct subject for removal', () => {
      const result = getEmailSubject(
        1,
        'removal',
        testDate,
        clientId,
        lastName,
        address,
      );

      expect(result).toBe(
        `Removal ->  ${address}, ${dayjs(testDate).format(DATE_FORMAT_ABBREVIATION)}_${clientId}, ${lastName}`,
      );
    });

    it('should return empty string for unknown level', () => {
      const result = getEmailSubject(
        1,
        'unknown',
        testDate,
        clientId,
        lastName,
        address,
      );

      expect(result).toBe('');
    });
  });
});
