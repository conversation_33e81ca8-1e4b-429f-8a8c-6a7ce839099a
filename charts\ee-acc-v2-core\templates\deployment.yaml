{{- if .Values.knativeDeploy }}
{{- else }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "fullname" . }}
  labels:
    draft: {{ default "draft-app" .Values.draft }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}"
spec:
  selector:
    matchLabels:
      app: {{ template "fullname" . }}           
  replicas: {{ .Values.replicaCount }}
  template:
    metadata:
      labels:
        draft: {{ default "draft-app" .Values.draft }}
        app: {{ template "fullname" . }}
{{- if .Values.podAnnotations }}
      annotations:
{{ toYaml .Values.podAnnotations | indent 8 }}
{{- end }}
    spec:
{{- if .Values.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.nodeSelector | indent 8 }}
{{- end }}     
{{- if .Values.affinity }}
      affinity: {{- include "template.tplValue" (dict "value" .Values.affinity "context" .) | nindent 8 }}
{{- end }}  
    {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
{{ toYaml .Values.imagePullSecrets | indent 8 }}
      {{- end }}       
      containers:
      - name: {{ .Chart.Name }}
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        env:
{{- range $pkey, $pval := .Values.env }}
        - name: {{ $pkey }}
          value: {{ quote $pval }}
{{- end }}
        - name: DATABASE_URI  
          valueFrom:
            secretKeyRef:
              name: mongodbatlas
              key: url            
        ports:
        - containerPort: {{ .Values.service.internalPort }}
        securityContext:
          privileged: true
        # readinessProbe:
        #   tcpSocket:
        #     port: {{ .Values.service.internalPort }}
        #   initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
        #   periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
        #   successThreshold: {{ .Values.readinessProbe.successThreshold }}
        #   timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
        # livenessProbe:
        #   tcpSocket:
        #     port: {{ .Values.service.internalPort }}
        #   initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
        #   periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
        #   successThreshold: {{ .Values.livenessProbe.successThreshold }}
        #   timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
        # livenessProbe:
        #   httpGet:
        #     path: {{ .Values.probePath }}
        #     port: {{ .Values.service.internalPort }}
        #   initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
        #   periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
        #   successThreshold: {{ .Values.livenessProbe.successThreshold }}
        #   timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
        # readinessProbe:
        #   httpGet:
        #     path: {{ .Values.probePath }}
        #     port: {{ .Values.service.internalPort }}
        #   initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
        #   periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
        #   successThreshold: {{ .Values.readinessProbe.successThreshold }}
        #   timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
        resources:
{{ toYaml .Values.resources | indent 12 }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
{{- end }}