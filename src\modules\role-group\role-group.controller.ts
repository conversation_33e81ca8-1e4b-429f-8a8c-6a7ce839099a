import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
} from '@nestjs/common';

import { RoleGroupModel } from './role-group.model';
import { RoleGroupService } from './role-group.service';

@Controller('role-group')
export class RoleGroupController {
  constructor(private readonly roleGroupService: RoleGroupService) {}

  @Post()
  create(@Body() data: Partial<RoleGroupModel>) {
    return this.roleGroupService.create(data);
  }

  @Get()
  findAll() {
    return this.roleGroupService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.roleGroupService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() data: Partial<RoleGroupModel>) {
    return this.roleGroupService.update(id, data);
  }

  @Delete(':id')
  delete(@Param('id') id: string) {
    return this.roleGroupService.delete(id);
  }
}
